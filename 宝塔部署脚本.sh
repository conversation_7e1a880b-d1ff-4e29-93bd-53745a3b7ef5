#!/bin/bash

# 宝塔服务器部署脚本
# 使用方法: ./宝塔部署脚本.sh [域名]
# 例如: ./宝塔部署脚本.sh yourdomain.com

set -e

# 配置变量
DOMAIN=${1:-"yourdomain.com"}
PROJECT_DIR="/www/wwwroot/video-review"
ADMIN_DOMAIN="admin.$DOMAIN"
API_DOMAIN="api.$DOMAIN"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "请使用root用户运行此脚本"
    fi
}

# 检查宝塔面板
check_bt_panel() {
    if ! command -v bt &> /dev/null; then
        error "未检测到宝塔面板，请先安装宝塔面板"
    fi
    log "宝塔面板检查通过"
}

# 检查必要软件
check_software() {
    log "检查必要软件..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        error "请在宝塔面板中安装Node.js版本管理器"
    fi
    
    # 检查PM2
    if ! command -v pm2 &> /dev/null; then
        error "请在宝塔面板中安装PM2管理器"
    fi
    
    # 检查Nginx
    if ! command -v nginx &> /dev/null; then
        error "请在宝塔面板中安装Nginx"
    fi
    
    log "软件检查完成"
}

# 创建项目目录
create_directories() {
    log "创建项目目录..."
    
    mkdir -p $PROJECT_DIR
    mkdir -p $PROJECT_DIR/data
    mkdir -p $PROJECT_DIR/uploads
    mkdir -p $PROJECT_DIR/logs
    
    # 设置权限
    chmod 755 $PROJECT_DIR
    chmod 755 $PROJECT_DIR/data
    chmod 755 $PROJECT_DIR/uploads
    chmod 755 $PROJECT_DIR/logs
    
    log "目录创建完成"
}

# 安装项目依赖
install_dependencies() {
    log "安装项目依赖..."
    
    cd $PROJECT_DIR/web-admin
    
    # 安装生产依赖
    npm install --production --silent
    
    log "依赖安装完成"
}

# 构建前端
build_frontend() {
    log "构建前端项目..."
    
    cd $PROJECT_DIR/web-admin
    
    # 设置环境变量
    export NODE_ENV=production
    export VITE_API_BASE_URL="https://$API_DOMAIN"
    
    # 构建
    npm run build
    
    if [[ ! -d "dist" ]]; then
        error "前端构建失败"
    fi
    
    log "前端构建完成"
}

# 配置PM2
setup_pm2() {
    log "配置PM2进程管理..."
    
    cd $PROJECT_DIR/web-admin
    
    # 创建PM2配置文件
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'video-review-api',
    script: './server.cjs',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      DB_PATH: '$PROJECT_DIR/data/db.sqlite3'
    },
    error_file: '$PROJECT_DIR/logs/err.log',
    out_file: '$PROJECT_DIR/logs/out.log',
    log_file: '$PROJECT_DIR/logs/combined.log',
    time: true,
    autorestart: true,
    max_memory_restart: '1G'
  }]
};
EOF
    
    # 启动PM2
    pm2 start ecosystem.config.js
    pm2 save
    
    log "PM2配置完成"
}

# 创建Nginx配置
create_nginx_config() {
    log "创建Nginx配置..."
    
    # 管理后台配置
    cat > /www/server/panel/vhost/nginx/$ADMIN_DOMAIN.conf << EOF
server {
    listen 80;
    server_name $ADMIN_DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $ADMIN_DOMAIN;
    root $PROJECT_DIR/web-admin/dist;
    index index.html;
    
    # SSL配置将由宝塔面板自动添加
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 前端路由
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

    # API接口配置
    cat > /www/server/panel/vhost/nginx/$API_DOMAIN.conf << EOF
server {
    listen 80;
    server_name $API_DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $API_DOMAIN;
    
    # SSL配置将由宝塔面板自动添加
    
    # 客户端最大上传大小
    client_max_body_size 100M;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # 视频文件访问
    location /uploads/ {
        alias $PROJECT_DIR/uploads/;
        expires 30d;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin *;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:3000/api/health;
        access_log off;
    }
}
EOF
    
    # 重载Nginx
    nginx -t && nginx -s reload
    
    log "Nginx配置创建完成"
}

# 创建数据库备份脚本
create_backup_script() {
    log "创建备份脚本..."
    
    cat > /www/server/panel/script/video_review_backup.sh << 'EOF'
#!/bin/bash

# 视频审核系统备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/www/backup/video-review"
PROJECT_DIR="/www/wwwroot/video-review"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
if [[ -f "$PROJECT_DIR/data/db.sqlite3" ]]; then
    cp "$PROJECT_DIR/data/db.sqlite3" "$BACKUP_DIR/db_backup_$DATE.sqlite3"
    echo "数据库备份完成: db_backup_$DATE.sqlite3"
fi

# 备份上传文件（可选，文件较大时建议单独处理）
# tar -czf "$BACKUP_DIR/uploads_backup_$DATE.tar.gz" -C "$PROJECT_DIR" uploads/

# 清理30天前的备份
find $BACKUP_DIR -name "db_backup_*.sqlite3" -mtime +30 -delete
find $BACKUP_DIR -name "uploads_backup_*.tar.gz" -mtime +30 -delete

echo "备份任务完成"
EOF
    
    chmod +x /www/server/panel/script/video_review_backup.sh
    
    log "备份脚本创建完成"
}

# 设置防火墙
setup_firewall() {
    log "配置防火墙..."
    
    # 开放必要端口
    if command -v ufw &> /dev/null; then
        ufw allow 22
        ufw allow 80
        ufw allow 443
        ufw allow 8888
        ufw --force enable
    fi
    
    log "防火墙配置完成"
}

# 验证部署
verify_deployment() {
    log "验证部署..."
    
    # 检查PM2进程
    if ! pm2 list | grep -q "video-review-api"; then
        error "PM2进程未启动"
    fi
    
    # 检查端口监听
    if ! netstat -tlnp | grep -q ":3000"; then
        error "API服务未启动"
    fi
    
    # 检查前端文件
    if [[ ! -f "$PROJECT_DIR/web-admin/dist/index.html" ]]; then
        error "前端文件不存在"
    fi
    
    log "部署验证通过"
}

# 显示部署信息
show_deployment_info() {
    log "部署完成！"
    echo ""
    echo "==================================="
    echo "📱 小程序配置信息："
    echo "   API域名: https://$API_DOMAIN"
    echo ""
    echo "🖥️  管理后台："
    echo "   访问地址: https://$ADMIN_DOMAIN"
    echo "   默认账号: admin"
    echo "   默认密码: password"
    echo ""
    echo "🔧 后续操作："
    echo "1. 在宝塔面板中为域名申请SSL证书"
    echo "2. 在微信公众平台配置服务器域名"
    echo "3. 修改小程序中的API地址"
    echo "4. 设置定时备份任务"
    echo "==================================="
}

# 主函数
main() {
    log "开始部署视频审核系统到宝塔服务器..."
    log "域名: $DOMAIN"
    log "管理后台: $ADMIN_DOMAIN"
    log "API接口: $API_DOMAIN"
    
    check_root
    check_bt_panel
    check_software
    create_directories
    
    # 如果项目文件不存在，提示用户上传
    if [[ ! -f "$PROJECT_DIR/web-admin/package.json" ]]; then
        warning "项目文件不存在，请先上传项目文件到 $PROJECT_DIR"
        info "可以通过以下方式上传："
        info "1. 使用宝塔面板文件管理器上传"
        info "2. 使用Git克隆: git clone <repo-url> $PROJECT_DIR"
        exit 1
    fi
    
    install_dependencies
    build_frontend
    setup_pm2
    create_nginx_config
    create_backup_script
    setup_firewall
    verify_deployment
    show_deployment_info
}

# 执行主函数
main "$@"
