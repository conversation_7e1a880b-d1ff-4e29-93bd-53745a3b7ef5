{"name": "getopts", "version": "2.3.0", "type": "module", "main": "index.cjs", "types": "index.d.ts", "description": "Parse CLI arguments.", "repository": "jorgebucaran/getopts", "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "files": ["*.*(c)[tj]s"], "author": "<PERSON>", "keywords": ["cli", "argv", "flags", "parse", "getopts", "minimist", "cli-parser"], "scripts": {"test": "c8 twist tests/*.js", "build": "node -e \"fs.writeFileSync('index.cjs',fs.readFileSync('index.js','utf8').replace(/export default/,'module.exports ='),'utf8')\"", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}}