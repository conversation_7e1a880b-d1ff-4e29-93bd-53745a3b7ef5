{"name": "iconv-lite", "description": "Convert character encodings in pure javascript.", "version": "0.6.3", "license": "MIT", "keywords": ["iconv", "convert", "charset", "icu"], "author": "<PERSON> <<EMAIL>>", "main": "./lib/index.js", "typings": "./lib/index.d.ts", "homepage": "https://github.com/ashtuchkin/iconv-lite", "bugs": "https://github.com/ashtuchkin/iconv-lite/issues", "repository": {"type": "git", "url": "git://github.com/ashtuchkin/iconv-lite.git"}, "engines": {"node": ">=0.10.0"}, "scripts": {"coverage": "c8 _mocha --grep .", "test": "mocha --reporter spec --grep ."}, "browser": {"stream": false}, "devDependencies": {"async": "^3.2.0", "c8": "^7.2.0", "errto": "^0.2.1", "iconv": "^2.3.5", "mocha": "^3.5.3", "request": "^2.88.2", "semver": "^6.3.0", "unorm": "^1.6.0"}, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}