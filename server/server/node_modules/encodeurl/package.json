{"name": "encodeurl", "description": "Encode a URL to a percent-encoded form, excluding already-encoded sequences", "version": "2.0.0", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "keywords": ["encode", "encodeurl", "url"], "repository": "pillarjs/encodeurl", "devDependencies": {"eslint": "5.11.1", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "2.5.3"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.8"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}}