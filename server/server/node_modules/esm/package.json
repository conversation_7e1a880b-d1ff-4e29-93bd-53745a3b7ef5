{"name": "esm", "version": "3.2.25", "description": "Tomorrow's ECMAScript modules today!", "keywords": "commonjs, ecmascript, export, import, modules, node, require", "repository": "standard-things/esm", "license": "MIT", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "main": "esm.js", "runkitExample": "require = require(\"esm\")(module)\nrequire(\"lodash-es\")", "engines": {"node": ">=6"}, "husky": {"hooks": {"precommit": "npm run lint"}}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "files": ["index.js", "esm.js", "esm/loader.js"]}