/* pages/upload/upload.wxss */
page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 40rpx 0;
}

.page-container {
  width: 90%;
  max-width: 700rpx;
  margin: 0 auto;
  padding: 50rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
  position: relative;
  overflow: hidden;
  height: 650rpx;
}

/* 上传区域 */
.upload-wrapper {
  width: 100%;
  margin-bottom: 10rpx;
}

.video-selector {
  width: 100%;
  height: 360rpx;
  border: 3rpx dashed #667eea;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.video-selector::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-selector:active::before {
  opacity: 1;
}

.video-selector:active {
  transform: scale(0.98);
  border-color: #5a67d8;
}

.video-selector .icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.7;
  z-index: 1;
}

.video-selector .tip {
  font-size: 32rpx;
  color: #667eea;
  font-weight: 600;
  z-index: 1;
}

.video-preview-wrapper {
  position: relative;
  width: 100%;
  height: 360rpx;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.video-preview-wrapper video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.re-choose-btn {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 24rpx;
  padding: 8rpx 15rpx;
  border-radius: 20rpx;
  z-index: 10;
  transition: background-color 0.2s ease;
}

.re-choose-btn:active {
  background-color: rgba(0, 0, 0, 0.7);
}

/* 表单区域 */
.form-wrapper {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 0 20rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.form-item {
  display: flex;
  align-items: center;
  height: 90rpx;
  border-bottom: 1rpx solid #e9ecef;
}

.form-item:last-child {
  border-bottom: none;
}

.form-item .label {
  width: 160rpx;
  font-size: 28rpx;
  color: #495057;
  font-weight: 500;
}

.form-item .input {
  flex: 1;
  font-size: 28rpx;
  color: #343a40;
  padding-left: 10rpx;
  background-color: transparent;
}

/* 操作区域 */
.action-wrapper {
  width: 100%;
  margin-top: 30rpx;
}

.progress-box {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
  background-color: #e9ecef;
  border-radius: 8rpx;
  padding: 12rpx 20rpx;
  box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.03);
}

.progress-box progress {
  flex: 1;
  height: 16rpx;
  border-radius: 8rpx;
  background-color: #dee2e6;
}

.progress-box progress .wx-progress-bar {
  background-color: #28a745; /* 绿色 */
  border-radius: 8rpx;
  transition: width 0.3s ease-out;
}

.progress-box .progress-text {
  font-size: 26rpx;
  color: #343a40;
  margin-left: 15rpx;
  width: 70rpx;
  text-align: right;
  font-weight: 500;
}

.upload-btn {
  width: 100%;
  height: 100rpx;
  line-height: 100rpx;
  border-radius: 50rpx;
  font-size: 36rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
  letter-spacing: 2rpx;
  border: none;
}

.upload-btn:active {
  transform: translateY(-2rpx);
  box-shadow: 0 15rpx 40rpx rgba(102, 126, 234, 0.4);
}

.upload-btn[disabled] {
  background: linear-gradient(135deg, #adb5bd 0%, #868e96 100%);
  color: rgba(255, 255, 255, 0.7);
  box-shadow: 0 5rpx 15rpx rgba(173, 181, 189, 0.2);
  transform: none;
}

.footer-tip {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 90%; /* 宽度调整 */
  text-align: center;
  font-size: 26rpx; /* 字体大小 */
  color: #6c757d; /* 柔和颜色 */
  font-weight: normal;
  letter-spacing: 0.5rpx;
  background-color: rgba(255, 255, 255, 0.7); /* 更透明的背景 */
  padding: 20rpx;
  border-radius: 10rpx;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.03);
  opacity: 0.9;
  z-index: 100;
}
