# 多阶段构建 Dockerfile

# 阶段1: 构建前端
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# 复制前端源码
COPY web-admin/package*.json ./
RUN npm ci --only=production

COPY web-admin/ ./

# 构建前端
ENV NODE_ENV=production
ENV VITE_API_BASE_URL=/api
RUN npm run build

# 阶段2: 生产环境
FROM node:18-alpine AS production

# 安装系统依赖
RUN apk add --no-cache \
    sqlite \
    dumb-init \
    && rm -rf /var/cache/apk/*

# 创建应用用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

WORKDIR /app

# 复制后端依赖文件
COPY web-admin/package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 复制后端源码
COPY web-admin/*.cjs ./
COPY web-admin/*.js ./

# 从构建阶段复制前端文件
COPY --from=frontend-builder /app/dist ./dist

# 创建必要目录
RUN mkdir -p uploads data logs && \
    chown -R nodejs:nodejs /app

# 切换到非 root 用户
USER nodejs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动命令
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "production-server.cjs"]
