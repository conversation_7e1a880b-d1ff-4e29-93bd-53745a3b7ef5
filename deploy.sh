#!/bin/bash

# 视频审核系统自动部署脚本
# 使用方法: ./deploy.sh [环境] [分支]
# 例如: ./deploy.sh production main

set -e  # 遇到错误立即退出

# 配置变量
ENVIRONMENT=${1:-production}
BRANCH=${2:-main}
PROJECT_DIR="/var/www/video-review"
BACKUP_DIR="/var/backups/video-review"
LOG_FILE="/var/log/deploy.log"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a $LOG_FILE
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a $LOG_FILE
}

# 检查权限
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        error "请不要使用 root 用户运行此脚本"
    fi
    
    if ! sudo -n true 2>/dev/null; then
        error "需要 sudo 权限，请确保当前用户在 sudoers 中"
    fi
}

# 检查依赖
check_dependencies() {
    log "检查系统依赖..."
    
    local deps=("node" "npm" "pm2" "nginx" "git")
    for dep in "${deps[@]}"; do
        if ! command -v $dep &> /dev/null; then
            error "缺少依赖: $dep"
        fi
    done
    
    # 检查 Node.js 版本
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $node_version -lt 16 ]]; then
        error "Node.js 版本过低，需要 16+ 版本"
    fi
    
    log "依赖检查完成"
}

# 备份数据库
backup_database() {
    log "备份数据库..."
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local db_file="$PROJECT_DIR/web-admin/db.sqlite3"
    
    if [[ -f $db_file ]]; then
        sudo mkdir -p $BACKUP_DIR
        sudo cp $db_file "$BACKUP_DIR/db_backup_$timestamp.sqlite3"
        log "数据库备份完成: db_backup_$timestamp.sqlite3"
        
        # 清理旧备份 (保留最近30天)
        sudo find $BACKUP_DIR -name "db_backup_*.sqlite3" -mtime +30 -delete
    else
        warning "数据库文件不存在，跳过备份"
    fi
}

# 拉取代码
pull_code() {
    log "拉取最新代码..."
    
    cd $PROJECT_DIR
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        warning "检测到未提交的更改，将被重置"
        git reset --hard HEAD
    fi
    
    # 切换分支并拉取
    git fetch origin
    git checkout $BRANCH
    git pull origin $BRANCH
    
    local commit_hash=$(git rev-parse --short HEAD)
    log "代码更新完成，当前提交: $commit_hash"
}

# 安装依赖
install_dependencies() {
    log "安装项目依赖..."
    
    cd $PROJECT_DIR/web-admin
    
    # 清理 node_modules (可选)
    if [[ "$CLEAN_INSTALL" == "true" ]]; then
        rm -rf node_modules package-lock.json
    fi
    
    npm ci --production --silent
    log "依赖安装完成"
}

# 构建前端
build_frontend() {
    log "构建前端项目..."
    
    cd $PROJECT_DIR/web-admin
    
    # 设置生产环境变量
    export NODE_ENV=production
    export VITE_API_BASE_URL="https://api.yourdomain.com"
    
    npm run build
    
    # 检查构建结果
    if [[ ! -d "dist" ]]; then
        error "前端构建失败，dist 目录不存在"
    fi
    
    log "前端构建完成"
}

# 更新服务
update_service() {
    log "更新后端服务..."
    
    cd $PROJECT_DIR/web-admin
    
    # 检查 PM2 进程
    if pm2 list | grep -q "video-review-api"; then
        pm2 reload ecosystem.config.js --env $ENVIRONMENT
        log "服务重载完成"
    else
        pm2 start ecosystem.config.js --env $ENVIRONMENT
        log "服务启动完成"
    fi
    
    # 等待服务启动
    sleep 5
    
    # 健康检查
    if ! curl -f http://localhost:3000/api/health &>/dev/null; then
        error "服务健康检查失败"
    fi
    
    log "服务运行正常"
}

# 更新 Nginx 配置
update_nginx() {
    log "更新 Nginx 配置..."
    
    # 测试配置文件
    sudo nginx -t
    if [[ $? -ne 0 ]]; then
        error "Nginx 配置文件有误"
    fi
    
    # 重载配置
    sudo nginx -s reload
    log "Nginx 配置更新完成"
}

# 清理缓存
cleanup() {
    log "清理缓存和临时文件..."
    
    # 清理 npm 缓存
    npm cache clean --force &>/dev/null || true
    
    # 清理日志文件 (保留最近7天)
    sudo find /var/log/video-review -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    # 清理上传的临时文件 (如果有)
    find $PROJECT_DIR/web-admin/uploads -name "tmp_*" -mtime +1 -delete 2>/dev/null || true
    
    log "清理完成"
}

# 验证部署
verify_deployment() {
    log "验证部署结果..."
    
    # 检查 API 健康状态
    local api_status=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/api/health)
    if [[ $api_status != "200" ]]; then
        error "API 健康检查失败 (HTTP $api_status)"
    fi
    
    # 检查前端文件
    if [[ ! -f "$PROJECT_DIR/web-admin/dist/index.html" ]]; then
        error "前端文件不存在"
    fi
    
    # 检查 PM2 进程状态
    if ! pm2 list | grep -q "online.*video-review-api"; then
        error "PM2 进程状态异常"
    fi
    
    log "部署验证通过"
}

# 发送通知 (可选)
send_notification() {
    local status=$1
    local message=$2
    
    # 这里可以集成钉钉、企业微信等通知
    # 示例：发送到钉钉机器人
    # curl -X POST "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN" \
    #      -H "Content-Type: application/json" \
    #      -d "{\"msgtype\":\"text\",\"text\":{\"content\":\"部署通知: $message\"}}"
    
    log "通知已发送: $message"
}

# 主函数
main() {
    log "开始部署视频审核系统 (环境: $ENVIRONMENT, 分支: $BRANCH)"
    
    # 执行部署步骤
    check_permissions
    check_dependencies
    backup_database
    pull_code
    install_dependencies
    build_frontend
    update_service
    update_nginx
    cleanup
    verify_deployment
    
    local end_time=$(date +'%Y-%m-%d %H:%M:%S')
    log "部署完成! 完成时间: $end_time"
    
    # 发送成功通知
    send_notification "success" "视频审核系统部署成功 ($ENVIRONMENT)"
}

# 错误处理
trap 'error "部署过程中发生错误，请检查日志: $LOG_FILE"' ERR

# 执行主函数
main "$@"
