// 测试API是否返回所有视频
const fetch = require('node-fetch');

async function testAPI() {
  console.log('🧪 测试视频API...');
  
  try {
    // 测试获取所有视频
    console.log('\n1️⃣ 测试获取所有视频 (无参数)');
    const response1 = await fetch('http://localhost:3000/api/admin/videos', {
      headers: {
        'Authorization': 'Bearer supersecrettoken',
      },
    });
    
    if (response1.ok) {
      const data1 = await response1.json();
      const videos1 = data1.videos || data1;
      console.log('✅ 返回视频数量:', videos1.length);
      videos1.forEach(video => {
        console.log(`   - ID: ${video.id}, 标题: ${video.title}, 状态: ${video.status}`);
      });
    } else {
      console.log('❌ 请求失败:', response1.status);
    }

    // 测试获取所有视频 (明确指定status=all)
    console.log('\n2️⃣ 测试获取所有视频 (status=all)');
    const response2 = await fetch('http://localhost:3000/api/admin/videos?status=all', {
      headers: {
        'Authorization': 'Bearer supersecrettoken',
      },
    });
    
    if (response2.ok) {
      const data2 = await response2.json();
      const videos2 = data2.videos || data2;
      console.log('✅ 返回视频数量:', videos2.length);
      videos2.forEach(video => {
        console.log(`   - ID: ${video.id}, 标题: ${video.title}, 状态: ${video.status}`);
      });
    } else {
      console.log('❌ 请求失败:', response2.status);
    }

    // 测试获取待审核视频
    console.log('\n3️⃣ 测试获取待审核视频 (status=pending)');
    const response3 = await fetch('http://localhost:3000/api/admin/videos?status=pending', {
      headers: {
        'Authorization': 'Bearer supersecrettoken',
      },
    });
    
    if (response3.ok) {
      const data3 = await response3.json();
      const videos3 = data3.videos || data3;
      console.log('✅ 返回视频数量:', videos3.length);
      videos3.forEach(video => {
        console.log(`   - ID: ${video.id}, 标题: ${video.title}, 状态: ${video.status}`);
      });
    } else {
      console.log('❌ 请求失败:', response3.status);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testAPI();
}

module.exports = testAPI;