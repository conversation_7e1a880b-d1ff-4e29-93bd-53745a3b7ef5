const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const knex = require('knex');
const cors = require('cors');

const app = express();
const port = 3000;

// --- 数据库配置 (Knex + SQLite) ---
const db = knex({
  client: 'sqlite3',
  connection: {
    filename: path.join(__dirname, 'db.sqlite3'),
  },
  useNullAsDefault: true,
});

// 启动时检查并创建数据表
async function setupDatabase() {
  // 创建videos表
  const videosTableExists = await db.schema.hasTable('videos');
  if (!videosTableExists) {
    console.log('Creating videos table...');
    await db.schema.createTable('videos', (table) => {
      table.increments('id').primary();
      table.string('filename').notNullable();
      table.string('url').notNullable();
      table.string('user_id').notNullable();
      table.string('title');
      table.string('status').defaultTo('pending');
      table.integer('review_deadline_hours').defaultTo(72); // 审核时效(小时)
      table.timestamps(true, true);
    });
    console.log('Videos table created.');
  }

  // 创建管理员表
  const adminsTableExists = await db.schema.hasTable('admins');
  if (!adminsTableExists) {
    console.log('Creating admins table...');
    await db.schema.createTable('admins', (table) => {
      table.increments('id').primary();
      table.string('username').unique().notNullable();
      table.string('password').notNullable();
      table.string('role').defaultTo('admin'); // admin, super_admin
      table.boolean('active').defaultTo(true);
      table.timestamps(true, true);
    });
    
    // 插入默认管理员账号
    await db('admins').insert({
      username: 'admin',
      password: 'password',
      role: 'super_admin',
      active: true
    });
    console.log('Admins table created with default admin account.');
  }

  // 创建系统设置表
  const settingsTableExists = await db.schema.hasTable('settings');
  if (!settingsTableExists) {
    console.log('Creating settings table...');
    await db.schema.createTable('settings', (table) => {
      table.increments('id').primary();
      table.string('key').unique().notNullable();
      table.text('value');
      table.string('description');
      table.timestamps(true, true);
    });
    
    // 插入默认设置
    await db('settings').insert([
      { key: 'default_review_deadline_hours', value: '72', description: '默认审核时效(小时)' },
      { key: 'auto_reject_expired', value: 'false', description: '是否自动拒绝过期视频' },
      { key: 'block_upload_when_expired', value: 'false', description: '是否在有过期视频时阻止新上传' }
    ]);
    console.log('Settings table created with default settings.');
  }

  // 创建审核记录表
  const reviewLogsTableExists = await db.schema.hasTable('review_logs');
  if (!reviewLogsTableExists) {
    console.log('Creating review_logs table...');
    await db.schema.createTable('review_logs', (table) => {
      table.increments('id').primary();
      table.integer('video_id').notNullable();
      table.integer('reviewer_id').notNullable();
      table.string('reviewer_username').notNullable();
      table.string('action').notNullable(); // 'approve', 'reject', 'pending'
      table.string('previous_status'); // 之前的状态
      table.string('new_status').notNullable(); // 新状态
      table.text('comment'); // 审核备注
      table.string('ip_address'); // 操作IP
      table.timestamps(true, true);
      
      // 外键关联
      table.foreign('video_id').references('id').inTable('videos').onDelete('CASCADE');
      table.foreign('reviewer_id').references('id').inTable('admins').onDelete('CASCADE');
    });
    console.log('Review logs table created.');
  }
}

// --- Multer 文件上传配置 ---
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir);
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const fileExtension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + fileExtension);
  },
});

const upload = multer({ storage: storage });

// --- 中间件 ---
app.use(cors()); // 启用 CORS
app.use(express.json()); // 用于解析 POST 请求的 JSON body
app.use('/uploads', express.static(path.join(__dirname, 'uploads'))); // 静态文件服务

// --- 认证中间件 ---
// 简单的认证，实际应用中应使用 JWT 或 Session
const AUTH_TOKEN = 'supersecrettoken'; // 硬编码的 token

function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (token == null) return res.status(401).json({ message: '未授权: 缺少 token' });

  if (token === AUTH_TOKEN) {
    next();
  } else {
    return res.status(403).json({ message: '未授权: token 无效' });
  }
}

// --- API 路由 ---

// 0. 登录接口
app.post('/api/login', async (req, res) => {
  const { username, password } = req.body;

  try {
    // 查询数据库中的管理员账号
    const admin = await db('admins')
      .where({ username, password, active: true })
      .first();

    if (admin) {
      res.json({ 
        message: '登录成功', 
        token: AUTH_TOKEN,
        user: {
          id: admin.id,
          username: admin.username,
          role: admin.role
        }
      });
    } else {
      res.status(401).json({ message: '用户名或密码错误' });
    }
  } catch (error) {
    console.error('登录验证失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 1. 视频上传接口 (无需认证)
app.post('/api/upload', upload.single('videoFile'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ message: '文件上传失败，没有找到文件' });
  }

  const { userId, title } = req.body; // 接收 title
  if (!userId) {
    return res.status(400).json({ message: '缺少 userId' });
  }

  const fileUrl = `http://localhost:${port}/uploads/${req.file.filename}`;

  try {
    const [newVideo] = await db('videos').insert({
      filename: req.file.filename,
      url: fileUrl,
      user_id: userId,
      title: title, // 保存 title
      status: 'pending',
    }).returning('*');

    console.log('视频信息已存入数据库:', newVideo);
    res.status(200).json({ message: '上传成功，等待审核', data: newVideo });
  } catch (error) {
    console.error('数据库插入失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 2. 获取所有视频列表 (需要认证)
app.get('/api/admin/videos', authenticateToken, async (req, res) => {
  try {
    const { status, search, limit = 200, offset = 0 } = req.query;
    
    let query = db('videos').orderBy('created_at', 'desc');
    
    console.log('API请求参数:', { status, search, limit, offset });
    
    // 状态筛选 - 只有明确指定状态且不是'all'时才筛选
    if (status && status !== 'all' && status !== '') {
      query = query.where('status', status);
      console.log('应用状态筛选:', status);
    } else {
      console.log('返回所有状态的视频');
    }
    
    // 搜索功能
    if (search) {
      query = query.where(function() {
        this.where('title', 'like', `%${search}%`)
            .orWhere('user_id', 'like', `%${search}%`)
            .orWhere('filename', 'like', `%${search}%`);
      });
    }
    
    // 分页
    const videos = await query.limit(parseInt(limit)).offset(parseInt(offset));
    console.log('查询到的视频数量:', videos.length);
    
    // 获取总数
    let countQuery = db('videos');
    if (status && status !== 'all') {
      countQuery = countQuery.where('status', status);
    }
    if (search) {
      countQuery = countQuery.where(function() {
        this.where('title', 'like', `%${search}%`)
            .orWhere('user_id', 'like', `%${search}%`)
            .orWhere('filename', 'like', `%${search}%`);
      });
    }
    const totalCount = await countQuery.count('* as count');
    
    res.status(200).json({
      videos,
      total: totalCount[0].count,
      hasMore: videos.length === parseInt(limit)
    });
  } catch (error) {
    console.error('查询视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 2.1 获取视频统计信息 (需要认证)
app.get('/api/admin/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await db('videos')
      .select('status')
      .count('* as count')
      .groupBy('status');
    
    const result = {
      total: 0,
      pending: 0,
      approved: 0,
      rejected: 0
    };
    
    stats.forEach(stat => {
      result[stat.status] = stat.count;
      result.total += stat.count;
    });
    
    res.status(200).json(result);
  } catch (error) {
    console.error('查询统计信息失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 3. 统一审核视频接口 (需要认证) - 带审核记录
app.post('/api/admin/review/:id', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const { action, comment } = req.body; // 'approve', 'reject', 'pending'

  if (!['approve', 'reject', 'pending'].includes(action)) {
    return res.status(400).json({ message: '无效的审核操作' });
  }

  try {
    // 获取当前用户信息（简化处理，实际应从token解析）
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    // 这里简化获取审核员信息，实际应该从token中解析
    const reviewer = await db('admins').where('active', true).first();
    if (!reviewer) {
      return res.status(401).json({ message: '无法获取审核员信息' });
    }
    
    // 获取视频当前状态
    const video = await db('videos').where('id', id).first();
    if (!video) {
      return res.status(404).json({ message: '视频不存在' });
    }

    const previousStatus = video.status;
    const newStatus = action === 'approve' ? 'approved' : action === 'reject' ? 'rejected' : 'pending';
    
    // 获取客户端IP
    const clientIP = req.ip || req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';

    // 开始事务，同时更新视频状态和记录审核日志
    await db.transaction(async (trx) => {
      // 更新视频状态
      await trx('videos').where('id', id).update({ 
        status: newStatus, 
        updated_at: new Date() 
      });

      // 记录审核日志
      await trx('review_logs').insert({
        video_id: parseInt(id),
        reviewer_id: reviewer.id,
        reviewer_username: reviewer.username,
        action: action,
        previous_status: previousStatus,
        new_status: newStatus,
        comment: comment || null,
        ip_address: clientIP,
        created_at: new Date(),
        updated_at: new Date()
      });
    });

    const actionText = action === 'approve' ? '批准' : action === 'reject' ? '拒绝' : '重置为待审核';
    console.log(`视频 ${id} 被审核员 ${reviewer.username} ${actionText} (${previousStatus} -> ${newStatus})`);
    
    res.status(200).json({ 
      message: `视频已${actionText}`,
      reviewer: reviewer.username,
      previousStatus,
      newStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('审核视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 3.1 获取视频审核记录
app.get('/api/admin/review-logs/:videoId', authenticateToken, async (req, res) => {
  try {
    const { videoId } = req.params;
    
    const logs = await db('review_logs')
      .where('video_id', videoId)
      .orderBy('created_at', 'desc');
    
    res.json(logs);
  } catch (error) {
    console.error('获取审核记录失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 3.2 获取审核员操作记录
app.get('/api/admin/reviewer-logs/:reviewerId', authenticateToken, async (req, res) => {
  try {
    const { reviewerId } = req.params;
    const { limit = 50, offset = 0 } = req.query;
    
    const logs = await db('review_logs')
      .leftJoin('videos', 'review_logs.video_id', 'videos.id')
      .where('review_logs.reviewer_id', reviewerId)
      .select(
        'review_logs.*',
        'videos.title as video_title',
        'videos.user_id as video_user_id'
      )
      .orderBy('review_logs.created_at', 'desc')
      .limit(parseInt(limit))
      .offset(parseInt(offset));
    
    res.json(logs);
  } catch (error) {
    console.error('获取审核员记录失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 保留原有接口以兼容现有代码
// 4. 批准视频 (需要认证) - 兼容接口
app.post('/api/videos/:id/approve', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const { comment } = req.body;
  
  try {
    // 获取当前用户信息
    const reviewer = await db('admins').where('active', true).first();
    if (!reviewer) {
      return res.status(401).json({ message: '无法获取审核员信息' });
    }
    
    // 获取视频当前状态
    const video = await db('videos').where('id', id).first();
    if (!video) {
      return res.status(404).json({ message: '视频不存在' });
    }

    const previousStatus = video.status;
    const newStatus = 'approved';
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';

    // 事务处理
    await db.transaction(async (trx) => {
      await trx('videos').where('id', id).update({ 
        status: newStatus, 
        updated_at: new Date() 
      });

      await trx('review_logs').insert({
        video_id: parseInt(id),
        reviewer_id: reviewer.id,
        reviewer_username: reviewer.username,
        action: 'approve',
        previous_status: previousStatus,
        new_status: newStatus,
        comment: comment || null,
        ip_address: clientIP,
        created_at: new Date(),
        updated_at: new Date()
      });
    });

    res.status(200).json({ 
      message: '视频已批准',
      reviewer: reviewer.username,
      previousStatus,
      newStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('批准视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 5. 拒绝视频 (需要认证) - 兼容接口
app.post('/api/videos/:id/reject', authenticateToken, async (req, res) => {
  const { id } = req.params;
  const { comment } = req.body;
  
  try {
    // 获取当前用户信息
    const reviewer = await db('admins').where('active', true).first();
    if (!reviewer) {
      return res.status(401).json({ message: '无法获取审核员信息' });
    }
    
    // 获取视频当前状态
    const video = await db('videos').where('id', id).first();
    if (!video) {
      return res.status(404).json({ message: '视频不存在' });
    }

    const previousStatus = video.status;
    const newStatus = 'rejected';
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';

    // 事务处理
    await db.transaction(async (trx) => {
      await trx('videos').where('id', id).update({ 
        status: newStatus, 
        updated_at: new Date() 
      });

      await trx('review_logs').insert({
        video_id: parseInt(id),
        reviewer_id: reviewer.id,
        reviewer_username: reviewer.username,
        action: 'reject',
        previous_status: previousStatus,
        new_status: newStatus,
        comment: comment || null,
        ip_address: clientIP,
        created_at: new Date(),
        updated_at: new Date()
      });
    });

    res.status(200).json({ 
      message: '视频已拒绝',
      reviewer: reviewer.username,
      previousStatus,
      newStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('拒绝视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 5. 获取指定用户上传的视频列表 (无需认证，小程序端使用)
app.get('/api/my/videos', async (req, res) => {
  try {
    const { userId } = req.query;
    if (!userId) {
      return res.status(400).json({ message: '缺少 userId' });
    }
    const userVideos = await db('videos').where('user_id', userId).orderBy('created_at', 'desc');
    res.status(200).json(userVideos);
  } catch (error) {
    console.error('查询用户视频失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 6. 管理员管理接口 (需要超级管理员权限)
app.get('/api/admin/admins', authenticateToken, async (req, res) => {
  try {
    const admins = await db('admins')
      .select('id', 'username', 'role', 'active', 'created_at')
      .orderBy('created_at', 'desc');
    res.json(admins);
  } catch (error) {
    console.error('获取管理员列表失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

app.post('/api/admin/admins', authenticateToken, async (req, res) => {
  try {
    const { username, password, role = 'admin' } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ message: '用户名和密码不能为空' });
    }

    // 检查用户名是否已存在
    const existingAdmin = await db('admins').where('username', username).first();
    if (existingAdmin) {
      return res.status(400).json({ message: '用户名已存在' });
    }

    const [newAdmin] = await db('admins').insert({
      username,
      password,
      role,
      active: true
    }).returning(['id', 'username', 'role', 'active', 'created_at']);

    res.json({ message: '管理员创建成功', admin: newAdmin });
  } catch (error) {
    console.error('创建管理员失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

app.put('/api/admin/admins/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { username, password, role, active } = req.body;
    
    const updateData = {};
    if (username) updateData.username = username;
    if (password) updateData.password = password;
    if (role) updateData.role = role;
    if (typeof active === 'boolean') updateData.active = active;

    const updated = await db('admins').where('id', id).update(updateData);
    if (updated) {
      res.json({ message: '管理员信息更新成功' });
    } else {
      res.status(404).json({ message: '管理员不存在' });
    }
  } catch (error) {
    console.error('更新管理员失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 7. 系统设置接口
app.get('/api/admin/settings', authenticateToken, async (req, res) => {
  try {
    const settings = await db('settings').select('*');
    const settingsObj = {};
    settings.forEach(setting => {
      settingsObj[setting.key] = setting.value;
    });
    res.json(settingsObj);
  } catch (error) {
    console.error('获取系统设置失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 7.1 检查过期视频状态
app.get('/api/admin/expired-status', authenticateToken, async (req, res) => {
  try {
    const settings = await db('settings').select('*');
    const settingsObj = {};
    settings.forEach(setting => {
      settingsObj[setting.key] = setting.value;
    });

    const now = new Date();
    const expiredVideos = await db('videos')
      .where('status', 'pending')
      .whereRaw(`datetime(created_at, '+' || review_deadline_hours || ' hours') < datetime(?)`, [now.toISOString()]);

    const blockUpload = settingsObj.block_upload_when_expired === 'true';
    
    res.json({
      expiredCount: expiredVideos.length,
      blockUpload: blockUpload,
      isBlocked: blockUpload && expiredVideos.length > 0,
      expiredVideos: expiredVideos.map(v => ({
        id: v.id,
        title: v.title,
        user_id: v.user_id,
        created_at: v.created_at,
        deadline_hours: v.review_deadline_hours
      }))
    });
  } catch (error) {
    console.error('检查过期状态失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

app.put('/api/admin/settings', authenticateToken, async (req, res) => {
  try {
    const settings = req.body;
    
    for (const [key, value] of Object.entries(settings)) {
      await db('settings')
        .where('key', key)
        .update({ value: String(value), updated_at: new Date() });
    }
    
    res.json({ message: '系统设置更新成功' });
  } catch (error) {
    console.error('更新系统设置失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// 8. 更新视频上传接口，支持审核时效和上传阻止
app.post('/api/upload', upload.single('videoFile'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ message: '文件上传失败，没有找到文件' });
  }

  const { userId, title } = req.body;
  if (!userId) {
    return res.status(400).json({ message: '缺少 userId' });
  }

  try {
    // 获取系统设置
    const settings = await db('settings').select('*');
    const settingsObj = {};
    settings.forEach(setting => {
      settingsObj[setting.key] = setting.value;
    });

    const blockUpload = settingsObj.block_upload_when_expired === 'true';
    const reviewDeadlineHours = parseInt(settingsObj.default_review_deadline_hours) || 72;

    // 如果开启了上传阻止，检查是否有过期的待审核视频
    if (blockUpload) {
      const now = new Date();
      const expiredVideos = await db('videos')
        .where('status', 'pending')
        .whereRaw(`datetime(created_at, '+' || review_deadline_hours || ' hours') < datetime(?)`, [now.toISOString()]);

      if (expiredVideos.length > 0) {
        console.log(`发现 ${expiredVideos.length} 个过期待审核视频，阻止新上传`);
        return res.status(423).json({ 
          message: `当前有 ${expiredVideos.length} 个视频审核超时，暂时无法上传新视频。请联系管理员处理。`,
          code: 'UPLOAD_BLOCKED_EXPIRED_VIDEOS',
          expiredCount: expiredVideos.length
        });
      }
    }

    const fileUrl = `http://localhost:${port}/uploads/${req.file.filename}`;

    const [newVideo] = await db('videos').insert({
      filename: req.file.filename,
      url: fileUrl,
      user_id: userId,
      title: title,
      status: 'pending',
      review_deadline_hours: reviewDeadlineHours
    }).returning('*');

    console.log('视频信息已存入数据库:', newVideo);
    res.status(200).json({ message: '上传成功，等待审核', data: newVideo });
  } catch (error) {
    console.error('数据库操作失败:', error);
    res.status(500).json({ message: '服务器内部错误' });
  }
});

// --- 启动服务器 ---
async function startServer() {
  await setupDatabase();
  app.listen(port, () => {
    console.log(`后台服务已启动，正在监听 http://localhost:${port}`);
  });
}

startServer();
