import React, { useState, useEffect } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css'; // 引入 Bootstrap CSS

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loginError, setLoginError] = useState(null);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [serverStatus, setServerStatus] = useState('checking'); // 'checking', 'online', 'offline'

  const [allVideos, setAllVideos] = useState([]);
  const [filteredVideos, setFilteredVideos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [statusFilter, setStatusFilter] = useState('all'); // 'all', 'pending', 'approved', 'rejected'
  const [searchTerm, setSearchTerm] = useState('');
  const [currentView, setCurrentView] = useState('videos'); // 'videos', 'admins', 'settings'
  const [admins, setAdmins] = useState([]);
  const [settings, setSettings] = useState({});
  const [currentUser, setCurrentUser] = useState(null);
  const [showAddAdmin, setShowAddAdmin] = useState(false);
  const [newAdmin, setNewAdmin] = useState({ username: '', password: '', role: 'admin' });
  const [systemSettings, setSystemSettings] = useState({
    default_review_deadline_hours: 72,
    auto_reject_expired: false,
    block_upload_when_expired: false
  });
  const [expiredStatus, setExpiredStatus] = useState({
    expiredCount: 0,
    blockUpload: false,
    isBlocked: false,
    expiredVideos: []
  });
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [showReviewLogs, setShowReviewLogs] = useState(false);
  const [selectedVideoLogs, setSelectedVideoLogs] = useState([]);
  const [reviewerLogs, setReviewerLogs] = useState([]);
  const [sortBy, setSortBy] = useState('smart'); // 'smart', 'newest', 'oldest', 'urgent'
  const [videoStats, setVideoStats] = useState({ pending: 0, approved: 0, rejected: 0 });
  const [urgentCount, setUrgentCount] = useState(0);

  // 检查服务器状态
  const checkServerStatus = async () => {
    try {
      const response = await fetch('http://localhost:3000/api/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username: 'test', password: 'test' }),
      });
      setServerStatus('online');
    } catch (error) {
      setServerStatus('offline');
    }
  };

  useEffect(() => {
    // 检查本地存储中是否有 token
    const token = localStorage.getItem('adminToken');
    if (token) {
      setIsLoggedIn(true);
    }
    
    // 检查服务器状态
    checkServerStatus();
  }, []);

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoginError(null);
    setIsLoggingIn(true);
    
    // 基本验证
    if (!username.trim() || !password.trim()) {
      setLoginError('请输入用户名和密码');
      setIsLoggingIn(false);
      return;
    }

    try {
      const response = await fetch('http://localhost:3000/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username: username.trim(), password }),
      });

      if (!response.ok) {
        let errorMessage = '登录失败，请检查网络连接';
        try {
          const errData = await response.json();
          errorMessage = errData.message || `服务器错误 (${response.status})`;
        } catch (parseErr) {
          console.error('解析错误响应失败:', parseErr);
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('🎯 登录响应数据:', data);
      
      if (data.token) {
        console.log('💾 保存token到localStorage:', data.token);
        localStorage.setItem('adminToken', data.token);
        
        // 保存用户信息
        if (data.user) {
          localStorage.setItem('currentUser', JSON.stringify(data.user));
          setCurrentUser(data.user);
          console.log('👤 当前用户:', data.user);
        }
        
        // 验证token是否正确保存
        const savedToken = localStorage.getItem('adminToken');
        console.log('✅ 验证保存的token:', savedToken);
        
        setIsLoggedIn(true);
        // 清空表单
        setUsername('');
        setPassword('');
      } else {
        console.error('❌ 服务器未返回token');
        throw new Error('服务器响应异常，未返回有效令牌');
      }
    } catch (err) {
      console.error('登录失败:', err);
      if (err.name === 'TypeError' && err.message.includes('fetch')) {
        setLoginError('无法连接到服务器，请检查服务器是否启动');
      } else {
        setLoginError(err.message);
      }
    } finally {
      setIsLoggingIn(false);
    }
  };

  const fetchAllVideos = async (status = 'all', search = '', sort = 'smart') => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('adminToken');
      console.log('🔑 当前token:', token);

      if (!token) {
        console.log('❌ 没有token，返回登录页');
        setIsLoggedIn(false);
        return;
      }

      // 构建查询参数
      const params = new URLSearchParams();
      if (status && status !== 'all') params.append('status', status);
      if (search && search.trim()) params.append('search', search.trim());
      params.append('sortBy', sort); // 添加排序参数
      params.append('limit', '200'); // 确保获取足够多的视频

      const url = `http://localhost:3000/api/admin/videos?${params}`;
      console.log('📡 发送请求到:', url, '排序方式:', sort);

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      console.log('📊 响应状态:', response.status);

      if (response.status === 401 || response.status === 403) {
        console.log('🚫 认证失败，清除token并返回登录页');
        localStorage.removeItem('adminToken');
        setIsLoggedIn(false);
        return;
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('✅ 获取视频数据成功:', data.videos?.length || data.length, '个视频');
      console.log('📊 统计信息:', data.stats, '紧急视频:', data.urgent_count);

      // 兼容新旧API格式
      const videos = data.videos || data;
      setAllVideos(videos);
      setFilteredVideos(videos);

      // 更新统计信息
      if (data.stats) {
        setVideoStats(data.stats);
      }
      if (data.urgent_count !== undefined) {
        setUrgentCount(data.urgent_count);
      }
    } catch (err) {
      console.error('❌ 获取视频列表失败:', err);
      setError('获取视频列表失败: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) return;

      const response = await fetch('http://localhost:3000/api/admin/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const stats = await response.json();
        console.log('📊 统计信息:', stats);
        return stats;
      }
    } catch (err) {
      console.error('获取统计信息失败:', err);
    }
    return null;
  };

  const fetchAdmins = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) return;

      const response = await fetch('http://localhost:3000/api/admin/admins', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const adminsList = await response.json();
        setAdmins(adminsList);
        console.log('👥 管理员列表:', adminsList);
      }
    } catch (err) {
      console.error('获取管理员列表失败:', err);
    }
  };

  const createAdmin = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) return;

      if (!newAdmin.username || !newAdmin.password) {
        alert('请填写完整的用户名和密码');
        return;
      }

      const response = await fetch('http://localhost:3000/api/admin/admins', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(newAdmin),
      });

      const data = await response.json();
      
      if (response.ok) {
        alert('✅ ' + data.message);
        setShowAddAdmin(false);
        setNewAdmin({ username: '', password: '', role: 'admin' });
        fetchAdmins(); // 刷新列表
      } else {
        alert('❌ ' + data.message);
      }
    } catch (err) {
      console.error('创建管理员失败:', err);
      alert('创建失败: ' + err.message);
    }
  };

  const toggleAdminStatus = async (adminId, currentStatus) => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) return;

      const response = await fetch(`http://localhost:3000/api/admin/admins/${adminId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ active: !currentStatus }),
      });

      const data = await response.json();
      
      if (response.ok) {
        alert('✅ ' + data.message);
        fetchAdmins(); // 刷新列表
      } else {
        alert('❌ ' + data.message);
      }
    } catch (err) {
      console.error('更新管理员状态失败:', err);
      alert('操作失败: ' + err.message);
    }
  };

  const fetchSystemSettings = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) return;

      const response = await fetch('http://localhost:3000/api/admin/settings', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const settings = await response.json();
        setSystemSettings({
          default_review_deadline_hours: parseInt(settings.default_review_deadline_hours) || 72,
          auto_reject_expired: settings.auto_reject_expired === 'true',
          block_upload_when_expired: settings.block_upload_when_expired === 'true'
        });
        console.log('⚙️ 系统设置:', settings);
      }
    } catch (err) {
      console.error('获取系统设置失败:', err);
    }
  };

  const saveSystemSettings = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) return;

      const response = await fetch('http://localhost:3000/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          default_review_deadline_hours: systemSettings.default_review_deadline_hours.toString(),
          auto_reject_expired: systemSettings.auto_reject_expired.toString(),
          block_upload_when_expired: systemSettings.block_upload_when_expired.toString()
        }),
      });

      const data = await response.json();
      
      if (response.ok) {
        alert('✅ ' + data.message);
        fetchExpiredStatus(); // 保存后刷新过期状态
      } else {
        alert('❌ ' + data.message);
      }
    } catch (err) {
      console.error('保存系统设置失败:', err);
      alert('保存失败: ' + err.message);
    }
  };

  const fetchExpiredStatus = async () => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) return;

      const response = await fetch('http://localhost:3000/api/admin/expired-status', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const status = await response.json();
        setExpiredStatus(status);
        console.log('⏰ 过期状态:', status);
      }
    } catch (err) {
      console.error('获取过期状态失败:', err);
    }
  };

  // 获取视频审核记录
  const fetchVideoReviewLogs = async (videoId) => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) return;

      const response = await fetch(`http://localhost:3000/api/admin/review-logs/${videoId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const logs = await response.json();
        setSelectedVideoLogs(logs);
        setShowReviewLogs(true);
        console.log('📋 视频审核记录:', logs);
      }
    } catch (err) {
      console.error('获取视频审核记录失败:', err);
    }
  };

  // 获取审核员操作记录
  const fetchReviewerLogs = async (reviewerId) => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) return;

      const response = await fetch(`http://localhost:3000/api/admin/reviewer-logs/${reviewerId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const logs = await response.json();
        setReviewerLogs(logs);
        console.log('👤 审核员操作记录:', logs);
      }
    } catch (err) {
      console.error('获取审核员记录失败:', err);
    }
  };

  useEffect(() => {
    if (isLoggedIn) {
      console.log('🔄 登录后初始加载，获取所有数据');
      fetchAllVideos('all', '', 'smart'); // 使用智能排序
      fetchStats();
      
      // 只有超级管理员才加载管理员列表和系统设置
      if (currentUser?.role === 'super_admin') {
        fetchAdmins();
        fetchSystemSettings();
      }
      
      // 普通审核员只能看视频管理页面
      if (currentUser?.role === 'admin') {
        setCurrentView('videos');
      }
    }
  }, [isLoggedIn, currentUser]);

  // 当切换到系统设置页面时加载数据
  useEffect(() => {
    if (isLoggedIn && currentView === 'settings' && currentUser?.role === 'super_admin') {
      fetchSystemSettings();
      fetchExpiredStatus();
    }
  }, [currentView]);

  // 定期检查过期状态（每5分钟）
  useEffect(() => {
    if (isLoggedIn && currentUser?.role === 'super_admin') {
      fetchExpiredStatus(); // 立即检查一次
      
      const interval = setInterval(() => {
        fetchExpiredStatus();
      }, 5 * 60 * 1000); // 5分钟检查一次

      return () => clearInterval(interval);
    }
  }, [isLoggedIn, currentUser]);

  // 当切换到管理员页面时加载数据
  useEffect(() => {
    if (isLoggedIn && currentView === 'admins') {
      fetchAdmins();
    }
  }, [currentView]);

  // 筛选、搜索和排序效果
  useEffect(() => {
    if (isLoggedIn) {
      console.log('🔄 筛选条件变化，重新获取视频', { statusFilter, searchTerm, sortBy });
      fetchAllVideos(statusFilter, searchTerm, sortBy);
    }
  }, [statusFilter, searchTerm, sortBy]);

  const handleReview = async (id, action, comment = '') => {
    try {
      const token = localStorage.getItem('adminToken');
      if (!token) {
        setIsLoggedIn(false);
        return;
      }

      // 使用原有的接口路径，现在已经包含审核记录功能
      const endpoint = action === 'approve' ? 'approve' : 'reject';
      const response = await fetch(`http://localhost:3000/api/videos/${id}/${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          comment: comment
        }),
      });

      if (response.status === 401 || response.status === 403) {
        localStorage.removeItem('adminToken');
        setIsLoggedIn(false);
        return;
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      // 审核成功后，刷新视频列表
      fetchAllVideos(statusFilter, searchTerm, sortBy);
      
      const actionText = action === 'approve' ? '批准' : '拒绝';
      alert(`✅ ${result.message}\n📋 审核员: ${result.reviewer}\n🕒 时间: ${new Date(result.timestamp).toLocaleString()}\n📊 状态变更: ${result.previousStatus} → ${result.newStatus}`);
      
      // 记录审核日志到控制台
      console.log('📝 审核记录已保存:', {
        videoId: id,
        action,
        reviewer: result.reviewer,
        previousStatus: result.previousStatus,
        newStatus: result.newStatus,
        timestamp: result.timestamp,
        comment: comment || '无备注'
      });
    } catch (err) {
      alert(`❌ 操作失败: ${err.message}`);
      console.error('审核操作失败:', err);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('adminToken');
    setIsLoggedIn(false);
    setUsername('');
    setPassword('');
  };

  if (!isLoggedIn) {
    return (
      <div className="login-container">
        <div className="login-card">
          <div className="text-center mb-4">
            <h2 className="login-title">🎬 管理员登录</h2>
            <p className="text-muted">视频审核管理后台</p>
            <div className="mt-2">
              {serverStatus === 'checking' && (
                <small className="text-info">🔄 检查服务器状态...</small>
              )}
              {serverStatus === 'online' && (
                <small className="text-success">✅ 服务器连接正常</small>
              )}
              {serverStatus === 'offline' && (
                <small className="text-danger">❌ 无法连接服务器，请检查后端是否启动</small>
              )}
            </div>
          </div>
          <form onSubmit={handleLogin}>
            <div className="login-form-group">
              <label htmlFor="usernameInput" className="login-label">👤 用户名</label>
              <input
                type="text"
                className="login-input"
                id="usernameInput"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
                placeholder="请输入用户名 (admin)"
                disabled={isLoggingIn}
                autoComplete="username"
              />
            </div>
            <div className="login-form-group">
              <label htmlFor="passwordInput" className="login-label">🔒 密码</label>
              <input
                type="password"
                className="login-input"
                id="passwordInput"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                placeholder="请输入密码 (password)"
                disabled={isLoggingIn}
                autoComplete="current-password"
              />
            </div>
            {loginError && (
              <div className="login-error">
                <strong>⚠️ 登录失败</strong><br/>
                {loginError}
              </div>
            )}
            <button 
              type="submit" 
              className="login-btn" 
              disabled={isLoggingIn || !username.trim() || !password.trim()}
            >
              {isLoggingIn ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                  正在登录...
                </>
              ) : (
                '🚀 立即登录'
              )}
            </button>
          </form>
          <div className="text-center mt-4">
            <div className="d-flex gap-2 justify-content-center mb-2">
              <button 
                type="button" 
                className="btn btn-outline-secondary btn-sm"
                onClick={() => {
                  setUsername('admin');
                  setPassword('password');
                }}
                disabled={isLoggingIn}
              >
                🔧 快速填充
              </button>
              <button 
                type="button" 
                className="btn btn-outline-info btn-sm"
                onClick={async () => {
                  console.log('🔍 开始调试认证...');
                  try {
                    // 清除现有token
                    localStorage.removeItem('adminToken');
                    
                    // 测试登录
                    const response = await fetch('http://localhost:3000/api/login', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({ username: 'admin', password: 'password' }),
                    });
                    
                    console.log('登录响应状态:', response.status);
                    const data = await response.json();
                    console.log('登录响应数据:', data);
                    
                    if (data.token) {
                      // 测试管理接口
                      const adminResponse = await fetch('http://localhost:3000/api/admin/videos', {
                        headers: { 'Authorization': `Bearer ${data.token}` },
                      });
                      console.log('管理接口状态:', adminResponse.status);
                      
                      if (adminResponse.ok) {
                        console.log('✅ 认证流程正常');
                      } else {
                        console.error('❌ 管理接口访问失败');
                      }
                    }
                  } catch (err) {
                    console.error('❌ 调试失败:', err);
                  }
                }}
                disabled={isLoggingIn}
              >
                🐛 调试认证
              </button>
            </div>
            <br/>
            <small className="text-muted">
              默认账号: admin / password
            </small>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="main-content" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', padding: '1.5rem' }}>
      <div className="container-fluid">
        <div className="row align-items-center mb-4">
          <div className="col-md-8">
            <h1 className="text-white fw-bold mb-2" style={{ fontSize: '3.5rem', textShadow: '0 2px 10px rgba(0,0,0,0.2)' }}>
              🎬 视频审核管理后台
            </h1>
            <p className="text-white fs-5 mb-0" style={{ opacity: 0.9 }}>
              专业的视频内容管理平台 - 全面掌控视频内容
            </p>
          </div>
          <div className="col-md-4 text-end">
            <div className="d-flex flex-column align-items-end gap-3">
              <div className="badge text-dark fs-4 rounded-pill px-4 py-3" style={{ background: 'rgba(255,255,255,0.9)' }}>
                📊 总计: {filteredVideos.length} 个视频
              </div>
              <div className="d-flex gap-2">
                <button
                  className="btn btn-outline-light rounded-pill px-3 py-2"
                  onClick={() => {
                    console.log('🔄 强制刷新所有视频');
                    fetchAllVideos(statusFilter, searchTerm, sortBy);
                  }}
                  style={{ backdropFilter: 'blur(10px)', background: 'rgba(255,255,255,0.1)', border: '2px solid rgba(255,255,255,0.3)' }}
                >
                  🔄 刷新
                </button>
                <button 
                  className="btn btn-outline-light rounded-pill px-5 py-3 fs-5" 
                  onClick={handleLogout} 
                  style={{ backdropFilter: 'blur(10px)', background: 'rgba(255,255,255,0.1)', border: '2px solid rgba(255,255,255,0.3)' }}
                >
                  🚪 退出登录
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 导航菜单 */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="card border-0 rounded-4 shadow-lg" style={{ background: 'rgba(255,255,255,0.95)', backdropFilter: 'blur(10px)' }}>
              <div className="card-body p-3">
                <div className="d-flex justify-content-between align-items-center">
                  <div className="d-flex gap-2">
                    <button 
                      className={`btn rounded-pill px-4 ${currentView === 'videos' ? 'btn-primary' : 'btn-outline-primary'}`}
                      onClick={() => setCurrentView('videos')}
                    >
                      🎬 视频管理
                    </button>
                    {currentUser?.role === 'super_admin' && (
                      <button 
                        className={`btn rounded-pill px-4 ${currentView === 'admins' ? 'btn-info' : 'btn-outline-info'}`}
                        onClick={() => setCurrentView('admins')}
                      >
                        👥 管理员
                      </button>
                    )}
                    {currentUser?.role === 'super_admin' && (
                      <button 
                        className={`btn rounded-pill px-4 ${currentView === 'settings' ? 'btn-secondary' : 'btn-outline-secondary'}`}
                        onClick={() => setCurrentView('settings')}
                      >
                        ⚙️ 系统设置
                      </button>
                    )}
                  </div>
                  <div className="text-muted">
                    👤 {currentUser?.username} ({currentUser?.role})
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 视频管理工具栏 - 重新设计 */}
        {currentView === 'videos' && (
          <div className="row mb-4">
            <div className="col-12">
              {/* 统计卡片区域 */}
              <div className="row mb-3">
                <div className="col-md-2">
                  <div
                    className="card border-0 rounded-4 shadow-sm h-100 stats-card"
                    style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}
                    onClick={() => setStatusFilter('all')}
                  >
                    <div className="card-body text-center text-white p-3">
                      <h3 className="mb-1">{allVideos.length}</h3>
                      <small>📋 全部视频</small>
                    </div>
                  </div>
                </div>
                <div className="col-md-2">
                  <div
                    className="card border-0 rounded-4 shadow-sm h-100 stats-card"
                    style={{ background: 'linear-gradient(135deg, #ffc107 0%, #ff8f00 100%)' }}
                    onClick={() => setStatusFilter('pending')}
                  >
                    <div className="card-body text-center text-white p-3">
                      <h3 className="mb-1">{videoStats.pending}</h3>
                      <small>⏳ 待审核</small>
                    </div>
                  </div>
                </div>
                <div className="col-md-2">
                  <div
                    className="card border-0 rounded-4 shadow-sm h-100 stats-card"
                    style={{ background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)' }}
                    onClick={() => setStatusFilter('approved')}
                  >
                    <div className="card-body text-center text-white p-3">
                      <h3 className="mb-1">{videoStats.approved}</h3>
                      <small>✅ 已通过</small>
                    </div>
                  </div>
                </div>
                <div className="col-md-2">
                  <div
                    className="card border-0 rounded-4 shadow-sm h-100 stats-card"
                    style={{ background: 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)' }}
                    onClick={() => setStatusFilter('rejected')}
                  >
                    <div className="card-body text-center text-white p-3">
                      <h3 className="mb-1">{videoStats.rejected}</h3>
                      <small>❌ 已拒绝</small>
                    </div>
                  </div>
                </div>
                <div className="col-md-2">
                  <div
                    className="card border-0 rounded-4 shadow-sm h-100 stats-card"
                    style={{ background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)' }}
                    onClick={() => setSortBy('urgent')}
                  >
                    <div className="card-body text-center text-white p-3">
                      <h3 className="mb-1">{urgentCount}</h3>
                      <small>🚨 紧急</small>
                    </div>
                  </div>
                </div>
                <div className="col-md-2">
                  <div
                    className="card border-0 rounded-4 shadow-sm h-100 stats-card"
                    style={{ background: 'linear-gradient(135deg, #9c88ff 0%, #8c7ae6 100%)' }}
                    onClick={() => setSortBy('smart')}
                  >
                    <div className="card-body text-center text-white p-3">
                      <h3 className="mb-1">🧠</h3>
                      <small>智能排序</small>
                    </div>
                  </div>
                </div>
              </div>

              {/* 筛选和搜索工具栏 */}
              <div className="card border-0 rounded-4 shadow-lg" style={{ background: 'rgba(255,255,255,0.95)', backdropFilter: 'blur(10px)' }}>
                <div className="card-body p-4">
                  <div className="row align-items-center">
                    <div className="col-md-8">
                      <div className="btn-group w-100" role="group">
                        <input 
                          type="radio" 
                          className="btn-check" 
                          name="statusFilter" 
                          id="filter-all" 
                          checked={statusFilter === 'all'}
                          onChange={() => setStatusFilter('all')}
                        />
                        <label className="btn btn-outline-primary" htmlFor="filter-all">
                          📋 全部视频
                        </label>

                        <input 
                          type="radio" 
                          className="btn-check" 
                          name="statusFilter" 
                          id="filter-pending" 
                          checked={statusFilter === 'pending'}
                          onChange={() => setStatusFilter('pending')}
                        />
                        <label className="btn btn-outline-warning" htmlFor="filter-pending">
                          ⏳ 待审核
                        </label>

                        <input 
                          type="radio" 
                          className="btn-check" 
                          name="statusFilter" 
                          id="filter-approved" 
                          checked={statusFilter === 'approved'}
                          onChange={() => setStatusFilter('approved')}
                        />
                        <label className="btn btn-outline-success" htmlFor="filter-approved">
                          ✅ 已通过
                        </label>

                        <input 
                          type="radio" 
                          className="btn-check" 
                          name="statusFilter" 
                          id="filter-rejected" 
                          checked={statusFilter === 'rejected'}
                          onChange={() => setStatusFilter('rejected')}
                        />
                        <label className="btn btn-outline-danger" htmlFor="filter-rejected">
                          ❌ 已拒绝
                        </label>
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="d-flex gap-2">
                        <div className="input-group flex-grow-1">
                          <span className="input-group-text bg-light border-0 rounded-start-pill">🔍</span>
                          <input
                            type="text"
                            className="form-control border-0 bg-light rounded-end-pill"
                            placeholder="搜索视频..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                          />
                          {searchTerm && (
                            <button
                              className="btn btn-outline-secondary border-0 position-absolute end-0 top-50 translate-middle-y me-2"
                              style={{ zIndex: 10 }}
                              onClick={() => setSearchTerm('')}
                            >
                              ✕
                            </button>
                          )}
                        </div>
                        <select
                          className="form-select rounded-pill"
                          style={{ maxWidth: '140px' }}
                          value={sortBy}
                          onChange={(e) => setSortBy(e.target.value)}
                          title="排序方式"
                        >
                          <option value="smart">🧠 智能排序</option>
                          <option value="urgent">🚨 紧急优先</option>
                          <option value="newest">🆕 最新优先</option>
                          <option value="oldest">⏰ 最早优先</option>
                        </select>
                        <div className="btn-group" role="group">
                          <button
                            className={`btn ${viewMode === 'grid' ? 'btn-primary' : 'btn-outline-primary'} rounded-start-pill`}
                            onClick={() => setViewMode('grid')}
                            title="网格视图"
                          >
                            ⊞
                          </button>
                          <button
                            className={`btn ${viewMode === 'list' ? 'btn-primary' : 'btn-outline-primary'} rounded-end-pill`}
                            onClick={() => setViewMode('list')}
                            title="列表视图"
                          >
                            ☰
                          </button>
                        </div>
                        <button
                          className="btn btn-outline-secondary rounded-pill px-3"
                          onClick={() => {
                            fetchAllVideos(statusFilter, searchTerm, sortBy);
                          }}
                          title="刷新数据"
                        >
                          🔄
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 管理员管理界面 */}
        {currentView === 'admins' && (
          <div className="row mb-4">
            <div className="col-12">
              <div className="card border-0 rounded-4 shadow-lg" style={{ background: 'rgba(255,255,255,0.95)', backdropFilter: 'blur(10px)' }}>
                <div className="card-body p-4">
                  <div className="d-flex justify-content-between align-items-center mb-4">
                    <h4 className="mb-0">👥 审核人员管理</h4>
                    <button 
                      className="btn btn-primary rounded-pill px-4" 
                      onClick={() => setShowAddAdmin(true)}
                    >
                      ➕ 添加审核员
                    </button>
                  </div>

                  {/* 添加管理员表单 */}
                  {showAddAdmin && (
                    <div className="card mb-4" style={{ background: 'rgba(102,126,234,0.1)', border: '1px solid rgba(102,126,234,0.3)' }}>
                      <div className="card-body">
                        <h6 className="card-title">➕ 新增审核员</h6>
                        <div className="row">
                          <div className="col-md-4">
                            <input
                              type="text"
                              className="form-control mb-3"
                              placeholder="用户名"
                              value={newAdmin.username}
                              onChange={(e) => setNewAdmin({...newAdmin, username: e.target.value})}
                            />
                          </div>
                          <div className="col-md-4">
                            <input
                              type="password"
                              className="form-control mb-3"
                              placeholder="密码"
                              value={newAdmin.password}
                              onChange={(e) => setNewAdmin({...newAdmin, password: e.target.value})}
                            />
                          </div>
                          <div className="col-md-4">
                            <select
                              className="form-select mb-3"
                              value={newAdmin.role}
                              onChange={(e) => setNewAdmin({...newAdmin, role: e.target.value})}
                            >
                              <option value="admin">普通审核员</option>
                              <option value="super_admin">超级管理员</option>
                            </select>
                          </div>
                        </div>
                        <div className="d-flex gap-2">
                          <button className="btn btn-success" onClick={createAdmin}>
                            ✅ 创建
                          </button>
                          <button 
                            className="btn btn-outline-secondary" 
                            onClick={() => {
                              setShowAddAdmin(false);
                              setNewAdmin({ username: '', password: '', role: 'admin' });
                            }}
                          >
                            ❌ 取消
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 管理员列表 */}
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead className="table-light">
                        <tr>
                          <th>ID</th>
                          <th>👤 用户名</th>
                          <th>🎭 角色</th>
                          <th>📊 状态</th>
                          <th>📅 创建时间</th>
                          <th>🔧 操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        {admins.length > 0 ? (
                          admins.map(admin => (
                            <tr key={admin.id}>
                              <td>{admin.id}</td>
                              <td>
                                <strong>{admin.username}</strong>
                                {admin.id === currentUser?.id && (
                                  <span className="badge bg-info ms-2">当前用户</span>
                                )}
                              </td>
                              <td>
                                <span className={`badge ${admin.role === 'super_admin' ? 'bg-danger' : 'bg-primary'}`}>
                                  {admin.role === 'super_admin' ? '🔑 超级管理员' : '👨‍💼 普通审核员'}
                                </span>
                              </td>
                              <td>
                                <span className={`badge ${admin.active ? 'bg-success' : 'bg-secondary'}`}>
                                  {admin.active ? '✅ 启用' : '❌ 禁用'}
                                </span>
                              </td>
                              <td>{new Date(admin.created_at).toLocaleString()}</td>
                              <td>
                                {admin.id !== currentUser?.id && (
                                  <button
                                    className={`btn btn-sm rounded-pill ${admin.active ? 'btn-outline-danger' : 'btn-outline-success'}`}
                                    onClick={() => toggleAdminStatus(admin.id, admin.active)}
                                  >
                                    {admin.active ? '🚫 禁用' : '✅启用'}
                                  </button>
                                )}
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan="6" className="text-center text-muted py-4">
                              📭 暂无审核员数据，点击上方按钮添加
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>

                  <div className="alert alert-info mt-4">
                    <h6>📋 审核员管理说明</h6>
                    <ul className="mb-0">
                      <li><strong>普通审核员</strong>: 只能审核视频，查看统计信息</li>
                      <li><strong>超级管理员</strong>: 可以管理审核员、修改系统设置</li>
                      <li><strong>状态管理</strong>: 可以启用/禁用审核员账号（不能操作自己）</li>
                      <li><strong>安全提示</strong>: 请妥善保管审核员账号密码</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 系统设置界面 */}
        {currentView === 'settings' && (
          <div className="row mb-4">
            <div className="col-12">
              <div className="card border-0 rounded-4 shadow-lg" style={{ background: 'rgba(255,255,255,0.95)', backdropFilter: 'blur(10px)' }}>
                <div className="card-body p-4">
                  <h4 className="mb-4">⚙️ 系统设置</h4>
                  <div className="row">
                    <div className="col-md-4">
                      <div className="mb-3">
                        <label className="form-label fw-bold">🕒 默认审核时效 (小时)</label>
                        <input 
                          type="number" 
                          className="form-control" 
                          value={systemSettings.default_review_deadline_hours}
                          onChange={(e) => setSystemSettings({
                            ...systemSettings,
                            default_review_deadline_hours: parseInt(e.target.value) || 72
                          })}
                          placeholder="请输入审核时效小时数"
                        />
                        <div className="form-text">视频上传后的审核期限</div>
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="mb-3">
                        <label className="form-label fw-bold">🤖 自动处理过期视频</label>
                        <select 
                          className="form-select"
                          value={systemSettings.auto_reject_expired}
                          onChange={(e) => setSystemSettings({
                            ...systemSettings,
                            auto_reject_expired: e.target.value === 'true'
                          })}
                        >
                          <option value="false">不自动处理</option>
                          <option value="true">自动拒绝过期视频</option>
                        </select>
                        <div className="form-text">超过审核时效的视频处理方式</div>
                      </div>
                    </div>
                    <div className="col-md-4">
                      <div className="mb-3">
                        <label className="form-label fw-bold">🚫 阻止过期后上传</label>
                        <select 
                          className="form-select"
                          value={systemSettings.block_upload_when_expired}
                          onChange={(e) => setSystemSettings({
                            ...systemSettings,
                            block_upload_when_expired: e.target.value === 'true'
                          })}
                        >
                          <option value="false">允许继续上传</option>
                          <option value="true">阻止新视频上传</option>
                        </select>
                        <div className="form-text">开启后，超过审核时效将禁止新上传</div>
                      </div>
                    </div>
                  </div>
                  
                  {/* 过期视频状态显示 */}
                  {expiredStatus.expiredCount > 0 && (
                    <div className={`alert ${expiredStatus.isBlocked ? 'alert-danger' : 'alert-warning'}`}>
                      <h6>⚠️ 过期视频提醒</h6>
                      <p className="mb-2">
                        当前有 <strong>{expiredStatus.expiredCount}</strong> 个视频审核超时
                        {expiredStatus.isBlocked && (
                          <span className="text-danger ms-2">
                            🚫 <strong>新视频上传已被阻止</strong>
                          </span>
                        )}
                      </p>
                      <small>请及时处理过期视频，或调整系统设置</small>
                    </div>
                  )}

                  <div className="alert alert-info">
                    <h6>📋 设置说明</h6>
                    <ul className="mb-0">
                      <li><strong>审核时效</strong>: 设置视频上传后的审核期限</li>
                      <li><strong>自动处理</strong>: 超时视频可自动拒绝，减少人工处理</li>
                      <li><strong>阻止上传</strong>: 开启后，当有视频超过审核时效时，将暂停接受新的视频上传</li>
                      <li><strong>建议</strong>: 合理设置时效，避免积压过多待审核视频</li>
                    </ul>
                  </div>
                  
                  <div className="d-flex gap-2">
                    <button 
                      className="btn btn-success rounded-pill px-4"
                      onClick={saveSystemSettings}
                    >
                      💾 保存设置
                    </button>
                    <button 
                      className="btn btn-outline-secondary rounded-pill px-4"
                      onClick={() => setSystemSettings({
                        default_review_deadline_hours: 72,
                        auto_reject_expired: false,
                        block_upload_when_expired: false
                      })}
                    >
                      🔄 重置默认
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {loading && (
        <div className="text-center mt-5">
          <div className="spinner-border text-white" role="status" style={{ width: '4rem', height: '4rem' }}>
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-4 fs-4 text-white">🔄 正在加载待审核视频...</p>
        </div>
      )}

      {error && (
        <div className="alert mt-5 text-center py-4 rounded-4 shadow" role="alert" style={{ background: 'rgba(255,255,255,0.9)', border: '2px solid #ff6b6b', color: '#dc3545' }}>
          <strong>⚠️ 错误!</strong> {error}
        </div>
      )}

      {!loading && !error && filteredVideos.length === 0 && (
        <div className="text-center mt-5">
          <div className="p-5 rounded-4 shadow-lg" style={{ background: 'rgba(255,255,255,0.9)', backdropFilter: 'blur(10px)' }}>
            <p className="lead fs-2 fw-bold mb-3" style={{ color: '#51cf66' }}>📭 没有找到符合条件的视频</p>
            <p className="fs-5" style={{ color: '#667eea' }}>尝试调整筛选条件或搜索关键词</p>
          </div>
        </div>
      )}

      {currentView === 'videos' && (
        <div>
          {console.log('🎬 当前显示的视频数量:', filteredVideos.length)}
          {filteredVideos.length === 0 ? (
            <div style={{ width: '100%', textAlign: 'center', padding: '2rem' }}>
              <div className="card border-0 rounded-4 shadow-lg" style={{ background: 'rgba(255,255,255,0.9)', backdropFilter: 'blur(10px)' }}>
                <div className="card-body p-5">
                  <h3 style={{ color: '#667eea' }}>📭 暂无视频数据</h3>
                  <p className="text-muted">请检查筛选条件或刷新页面</p>
                </div>
              </div>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="video-container">
              {filteredVideos.map(video => {
          const getStatusBadge = (status) => {
            switch(status) {
              case 'pending':
                return <span className="badge bg-warning text-dark rounded-pill px-3 py-2">⏳ 待审核</span>;
              case 'approved':
                return <span className="badge bg-success rounded-pill px-3 py-2">✅ 已通过</span>;
              case 'rejected':
                return <span className="badge bg-danger rounded-pill px-3 py-2">❌ 已拒绝</span>;
              default:
                return <span className="badge bg-secondary rounded-pill px-3 py-2">❓ 未知</span>;
            }
          };

          return (
          <div key={video.id} className={`video-card ${
            video.status === 'pending' && video.urgency_level ?
            `video-card-${video.urgency_level}` : ''
          }`}>
            <div className="card border-0 rounded-4 shadow-lg" style={{
              background: 'rgba(255,255,255,0.95)',
              backdropFilter: 'blur(10px)',
              border: video.status === 'pending' && video.urgency_level === 'critical' ?
                '2px solid #ff6b6b' : '1px solid rgba(255,255,255,0.2)',
              height: '100%',
              minHeight: '350px',
              display: 'flex',
              flexDirection: 'column'
            }}>
              <div className="card-body d-flex flex-column p-2">
                <div className="d-flex justify-content-between align-items-start mb-1">
                  <h6 className="card-title fw-bold mb-0" style={{ color: '#2c3e50', fontSize: '0.9rem', lineHeight: '1.2', flex: 1 }}>
                    📹 {video.title || '未命名视频'}
                  </h6>
                  <span className={`badge ${video.status === 'pending' ? 'bg-warning text-dark' : video.status === 'approved' ? 'bg-success' : 'bg-danger'} rounded-pill px-2 py-1`} style={{ fontSize: '0.7rem' }}>
                    {video.status === 'pending' ? '⏳' : video.status === 'approved' ? '✅' : '❌'}
                  </span>
                </div>
                
                <div className="mb-1">
                  <p className="card-text mb-0" style={{ color: '#667eea', fontSize: '0.7rem' }}>
                    👤 {video.user_id}
                  </p>
                  {video.status === 'pending' && video.urgency_level && (
                    <div className="mb-1">
                      <span className={`badge ${
                        video.urgency_level === 'critical' ? 'bg-danger' :
                        video.urgency_level === 'high' ? 'bg-warning text-dark' :
                        video.urgency_level === 'medium' ? 'bg-info' : 'bg-secondary'
                      } rounded-pill`} style={{ fontSize: '0.6rem' }}>
                        {video.urgency_level === 'critical' ? '🚨 紧急' :
                         video.urgency_level === 'high' ? '⚠️ 高' :
                         video.urgency_level === 'medium' ? '📊 中' : '🟢 低'}
                      </span>
                      {video.remaining_hours !== undefined && (
                        <small className="text-muted ms-1" style={{ fontSize: '0.6rem' }}>
                          {video.is_expired ? '⏰ 已超时' : `⏱️ ${video.remaining_hours}h`}
                        </small>
                      )}
                    </div>
                  )}
                  {video.status === 'pending' && video.progress_percentage !== undefined && (
                    <div className="progress mb-1" style={{ height: '3px' }}>
                      <div
                        className={`progress-bar ${
                          video.progress_percentage > 80 ? 'bg-danger' :
                          video.progress_percentage > 50 ? 'bg-warning' : 'bg-info'
                        }`}
                        style={{ width: `${video.progress_percentage}%` }}
                      ></div>
                    </div>
                  )}
                  <button
                    className="btn btn-link p-0"
                    style={{ fontSize: '0.6rem', color: '#6c757d' }}
                    onClick={() => fetchVideoReviewLogs(video.id)}
                  >
                    📋 查看记录
                  </button>
                </div>
                
                <div className="mb-2 rounded-3 overflow-hidden shadow" style={{ 
                  border: '2px solid rgba(102,126,234,0.2)',
                  height: '120px',
                  width: '100%'
                }}>
                  <video 
                    controls 
                    src={video.url} 
                    preload="metadata"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                  ></video>
                </div>
                
                <div className="mt-auto">
                  {video.status === 'pending' ? (
                    <div className="d-flex gap-1">
                      <button
                        className="btn rounded-pill shadow flex-fill"
                        style={{ 
                          background: 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)', 
                          border: 'none', 
                          color: 'white', 
                          fontWeight: 'bold', 
                          padding: '4px 8px',
                          fontSize: '0.7rem'
                        }}
                        onClick={() => handleReview(video.id, 'approve')}
                      >
                        ✅
                      </button>
                      <button
                        className="btn rounded-pill shadow flex-fill"
                        style={{ 
                          background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)', 
                          border: 'none', 
                          color: 'white', 
                          fontWeight: 'bold', 
                          padding: '4px 8px',
                          fontSize: '0.7rem'
                        }}
                        onClick={() => handleReview(video.id, 'reject')}
                      >
                        ❌
                      </button>
                    </div>
                  ) : (
                    <div className="d-flex gap-1">
                      {video.status === 'approved' && (
                        <button
                          className="btn btn-outline-danger rounded-pill flex-fill"
                          style={{ fontSize: '0.65rem', padding: '3px 6px' }}
                          onClick={() => handleReview(video.id, 'reject')}
                        >
                          🔄
                        </button>
                      )}
                      {video.status === 'rejected' && (
                        <button
                          className="btn btn-outline-success rounded-pill flex-fill"
                          style={{ fontSize: '0.65rem', padding: '3px 6px' }}
                          onClick={() => handleReview(video.id, 'approve')}
                        >
                          🔄
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          );
              })}
            </div>
          ) : (
            /* 列表视图 */
            <div className="row">
              <div className="col-12">
                <div className="card border-0 rounded-4 shadow-lg" style={{ background: 'rgba(255,255,255,0.95)', backdropFilter: 'blur(10px)' }}>
                  <div className="card-body p-0">
                    <div className="table-responsive">
                      <table className="table table-hover mb-0">
                        <thead className="table-light">
                          <tr>
                            <th style={{ width: '60px' }}>预览</th>
                            <th>标题</th>
                            <th style={{ width: '100px' }}>状态</th>
                            <th style={{ width: '120px' }}>用户</th>
                            <th style={{ width: '140px' }}>上传时间</th>
                            <th style={{ width: '160px' }}>操作</th>
                          </tr>
                        </thead>
                        <tbody>
                          {filteredVideos.map(video => (
                            <tr key={video.id}>
                              <td>
                                <div style={{ width: '50px', height: '35px', borderRadius: '6px', overflow: 'hidden' }}>
                                  <video 
                                    src={video.url} 
                                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                    muted
                                  />
                                </div>
                              </td>
                              <td>
                                <strong>{video.title || '未命名视频'}</strong>
                              </td>
                              <td>
                                <span className={`badge ${video.status === 'pending' ? 'bg-warning text-dark' : video.status === 'approved' ? 'bg-success' : 'bg-danger'} rounded-pill`}>
                                  {video.status === 'pending' ? '⏳ 待审核' : video.status === 'approved' ? '✅ 已通过' : '❌ 已拒绝'}
                                </span>
                              </td>
                              <td>{video.user_id}</td>
                              <td>{new Date(video.created_at).toLocaleDateString()}</td>
                              <td>
                                {video.status === 'pending' ? (
                                  <div className="d-flex gap-1">
                                    <button
                                      className="btn btn-sm rounded-pill"
                                      style={{ background: 'linear-gradient(135deg, #51cf66 0%, #40c057 100%)', border: 'none', color: 'white', fontSize: '0.7rem' }}
                                      onClick={() => handleReview(video.id, 'approve')}
                                    >
                                      ✅
                                    </button>
                                    <button
                                      className="btn btn-sm rounded-pill"
                                      style={{ background: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)', border: 'none', color: 'white', fontSize: '0.7rem' }}
                                      onClick={() => handleReview(video.id, 'reject')}
                                    >
                                      ❌
                                    </button>
                                  </div>
                                ) : (
                                  <button
                                    className="btn btn-sm btn-outline-secondary rounded-pill"
                                    style={{ fontSize: '0.7rem' }}
                                    onClick={() => handleReview(video.id, video.status === 'approved' ? 'reject' : 'approve')}
                                  >
                                    🔄
                                  </button>
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 审核记录查看弹窗 */}
      {showReviewLogs && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content border-0 rounded-4 shadow-lg">
              <div className="modal-header" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
                <h5 className="modal-title text-white">📋 视频审核记录</h5>
                <button 
                  type="button" 
                  className="btn-close btn-close-white" 
                  onClick={() => setShowReviewLogs(false)}
                ></button>
              </div>
              <div className="modal-body p-4">
                {selectedVideoLogs.length > 0 ? (
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead className="table-light">
                        <tr>
                          <th>时间</th>
                          <th>审核员</th>
                          <th>操作</th>
                          <th>状态变更</th>
                          <th>备注</th>
                          <th>IP地址</th>
                        </tr>
                      </thead>
                      <tbody>
                        {selectedVideoLogs.map((log, index) => (
                          <tr key={index}>
                            <td>{new Date(log.created_at).toLocaleString()}</td>
                            <td>
                              <span className="badge bg-primary rounded-pill">
                                👤 {log.reviewer_username}
                              </span>
                            </td>
                            <td>
                              <span className={`badge rounded-pill ${
                                log.action === 'approve' ? 'bg-success' : 
                                log.action === 'reject' ? 'bg-danger' : 'bg-warning text-dark'
                              }`}>
                                {log.action === 'approve' ? '✅ 批准' : 
                                 log.action === 'reject' ? '❌ 拒绝' : '⏳ 待审核'}
                              </span>
                            </td>
                            <td>
                              <small className="text-muted">
                                {log.previous_status} → {log.new_status}
                              </small>
                            </td>
                            <td>{log.comment || '无备注'}</td>
                            <td><small className="text-muted">{log.ip_address}</small></td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-muted">暂无审核记录</p>
                  </div>
                )}
              </div>
              <div className="modal-footer">
                <button 
                  type="button" 
                  className="btn btn-secondary rounded-pill px-4" 
                  onClick={() => setShowReviewLogs(false)}
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
}

export default App;
