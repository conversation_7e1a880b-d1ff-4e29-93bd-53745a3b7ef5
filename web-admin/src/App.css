/* 桌面端专用样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: auto;
  margin: 0;
  padding: 0;
  overflow-x: auto;
  overflow-y: auto;
  font-size: 14px; /* 桌面端标准字体大小 */
}

#root {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

/* 强制桌面端布局 */
.container-fluid {
  max-width: none !important;
  padding-left: 2rem !important;
  padding-right: 2rem !important;
}

/* 确保页面可以滚动 */
.main-content {
  width: 100%;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 桌面端卡片样式 */
.video-card-desktop {
  min-width: 300px;
  max-width: 350px;
}

/* 视频容器样式 - 固定一行4个 */
.video-container {
  display: grid !important;
  grid-template-columns: repeat(4, 1fr) !important;
  gap: 1.5rem !important;
  width: 100% !important;
  padding: 1rem 0 3rem 0 !important;
  overflow: visible !important;
}

.video-card {
  width: 100% !important;
  height: auto !important;
  min-height: 350px !important;
}

/* 响应式支持 */
@media (max-width: 1400px) {
  .video-container {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

@media (max-width: 1024px) {
  .video-container {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .video-container {
    grid-template-columns: repeat(1, 1fr) !important;
  }
}

/* 确保视频播放器不会导致布局问题 */
.video-player-container {
  width: 100% !important;
  height: 200px !important;
  overflow: hidden !important;
  position: relative !important;
}

.video-player-container video {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  display: block !important;
}

/* 新的分栏样式 */
.stats-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.2) !important;
}

.btn-group .btn-check:checked + .btn {
  background: var(--bs-primary) !important;
  border-color: var(--bs-primary) !important;
  color: white !important;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-group .btn {
  transition: all 0.3s ease;
  border-radius: 0;
}

.btn-group .btn:first-child {
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
}

.btn-group .btn:last-child {
  border-top-right-radius: 25px;
  border-bottom-right-radius: 25px;
}

.search-input-container {
  position: relative;
}

.search-clear-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  z-index: 10;
}

/* 视频卡片悬停效果 */
.card:hover {
  transform: translateY(-5px);
  transition: all 0.3s ease;
}

/* 按钮悬停效果 */
.btn:hover {
  transform: translateY(-2px);
  transition: all 0.2s ease;
}

/* 视频播放器样式 */
video {
  border-radius: 12px;
}

/* 登录页面样式 */
.login-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 2rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  padding: 3rem 2.5rem;
  width: 100%;
  max-width: 400px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.login-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.login-title {
  color: #2c3e50;
  font-weight: 700;
  font-size: 2rem;
  margin-bottom: 2rem;
  text-align: center;
}

.login-form-group {
  margin-bottom: 1.5rem;
}

.login-label {
  color: #5a6c7d;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.login-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid #e1e8ed;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  box-sizing: border-box;
}

.login-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 1);
}

.login-btn {
  width: 100%;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50px;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.login-btn:active:not(:disabled) {
  transform: translateY(0);
}

.login-btn:disabled {
  background: linear-gradient(135deg, #adb5bd 0%, #868e96 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.7;
}

.login-error {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.3);
  color: #dc3545;
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
  font-size: 0.9rem;
}

/* 紧急程度样式 */
.urgency-critical {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%) !important;
  animation: pulse 2s infinite;
}

.urgency-high {
  background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%) !important;
}

.urgency-medium {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

.urgency-low {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}

/* 进度条样式 */
.progress {
  background-color: rgba(0,0,0,0.1) !important;
  border-radius: 10px !important;
}

.progress-bar {
  border-radius: 10px !important;
  transition: width 0.6s ease;
}

/* 排序选择器样式 */
.form-select {
  border: 2px solid #e1e8ed;
  transition: all 0.3s ease;
}

.form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 智能排序提示 */
.smart-sort-indicator {
  position: relative;
}

.smart-sort-indicator::after {
  content: "🧠";
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 12px;
  background: #667eea;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 视频卡片边框根据紧急程度变色 */
.video-card-critical {
  border: 2px solid #ff6b6b !important;
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.3) !important;
}

.video-card-high {
  border: 2px solid #ffc107 !important;
  box-shadow: 0 0 15px rgba(255, 193, 7, 0.2) !important;
}

.video-card-medium {
  border: 2px solid #17a2b8 !important;
}

.video-card-low {
  border: 2px solid #28a745 !important;
}


