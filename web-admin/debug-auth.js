// 认证调试工具
async function debugAuth() {
  console.log('🔍 开始认证调试...');
  
  // 1. 测试登录
  console.log('\n1️⃣ 测试登录接口...');
  try {
    const loginResponse = await fetch('http://localhost:3000/api/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ username: 'admin', password: 'password' }),
    });
    
    console.log('登录响应状态:', loginResponse.status);
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('✅ 登录成功，获得数据:', loginData);
      
      if (loginData.token) {
        console.log('🔑 Token:', loginData.token);
        
        // 2. 测试使用token访问管理接口
        console.log('\n2️⃣ 测试管理接口访问...');
        
        const adminResponse = await fetch('http://localhost:3000/api/admin/videos', {
          headers: {
            'Authorization': `Bearer ${loginData.token}`,
          },
        });
        
        console.log('管理接口响应状态:', adminResponse.status);
        console.log('管理接口响应头:', Object.fromEntries(adminResponse.headers.entries()));
        
        if (adminResponse.ok) {
          const videos = await adminResponse.json();
          console.log('✅ 管理接口访问成功，视频数量:', videos.length);
        } else {
          const errorText = await adminResponse.text();
          console.error('❌ 管理接口访问失败:', errorText);
        }
        
        // 3. 测试后端token验证逻辑
        console.log('\n3️⃣ 验证token格式...');
        console.log('Token长度:', loginData.token.length);
        console.log('Token内容:', loginData.token);
        console.log('预期token:', 'supersecrettoken');
        console.log('Token匹配:', loginData.token === 'supersecrettoken');
        
      } else {
        console.error('❌ 登录响应中没有token');
      }
    } else {
      const errorData = await loginResponse.text();
      console.error('❌ 登录失败:', errorData);
    }
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  }
}

// 在浏览器控制台中运行
if (typeof window !== 'undefined') {
  window.debugAuth = debugAuth;
  console.log('🛠️ 调试工具已加载，在控制台运行 debugAuth() 开始调试');
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = debugAuth;
}