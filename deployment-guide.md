# 🚀 视频审核系统线上部署指南

## 1. 后台服务部署

### 方案一：云服务器部署 (推荐)

#### 1.1 服务器准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装 PM2 (进程管理器)
sudo npm install -g pm2

# 安装 Nginx (反向代理)
sudo apt install nginx -y

# 安装防火墙
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
```

#### 1.2 项目部署
```bash
# 创建项目目录
sudo mkdir -p /var/www/video-review
sudo chown $USER:$USER /var/www/video-review

# 上传项目文件 (使用 scp 或 git)
cd /var/www/video-review
git clone <your-repo-url> .

# 安装依赖
cd web-admin
npm install --production

# 创建生产环境配置
cp server.cjs production-server.cjs
```

#### 1.3 生产环境配置
```javascript
// production-server.cjs 修改要点：
const port = process.env.PORT || 3000;
const dbPath = process.env.DB_PATH || '/var/www/video-review/data/db.sqlite3';

// 添加安全中间件
app.use(helmet());
app.use(compression());

// 配置 CORS 为生产域名
app.use(cors({
  origin: ['https://yourdomain.com', 'https://admin.yourdomain.com'],
  credentials: true
}));
```

#### 1.4 PM2 配置
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'video-review-api',
    script: './production-server.cjs',
    cwd: '/var/www/video-review/web-admin',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      DB_PATH: '/var/www/video-review/data/db.sqlite3'
    },
    error_file: '/var/log/video-review/err.log',
    out_file: '/var/log/video-review/out.log',
    log_file: '/var/log/video-review/combined.log',
    time: true
  }]
};
```

#### 1.5 Nginx 配置
```nginx
# /etc/nginx/sites-available/video-review
server {
    listen 80;
    server_name api.yourdomain.com;
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 静态文件 (视频上传)
    location /uploads/ {
        alias /var/www/video-review/web-admin/uploads/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}

# 管理后台
server {
    listen 80;
    server_name admin.yourdomain.com;
    root /var/www/video-review/web-admin/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

#### 1.6 SSL 证书配置
```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取 SSL 证书
sudo certbot --nginx -d api.yourdomain.com -d admin.yourdomain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

### 方案二：Docker 部署

#### 2.1 Dockerfile
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制 package.json
COPY web-admin/package*.json ./
RUN npm install --production

# 复制源代码
COPY web-admin/ ./

# 创建数据目录
RUN mkdir -p /app/data /app/uploads

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["node", "production-server.cjs"]
```

#### 2.2 docker-compose.yml
```yaml
version: '3.8'
services:
  video-review-api:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
    environment:
      - NODE_ENV=production
      - DB_PATH=/app/data/db.sqlite3
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - video-review-api
    restart: unless-stopped
```

## 2. 小程序部署

### 2.1 配置修改
```javascript
// 修改 API 基础地址
// pages/upload/upload.js 和其他页面
const API_BASE = 'https://api.yourdomain.com';

// 上传接口
wx.uploadFile({
  url: `${API_BASE}/api/upload`,
  // ...
});

// 获取视频列表
wx.request({
  url: `${API_BASE}/api/my/videos`,
  // ...
});
```

### 2.2 域名配置
在微信公众平台配置服务器域名：
- request合法域名：`https://api.yourdomain.com`
- uploadFile合法域名：`https://api.yourdomain.com`
- downloadFile合法域名：`https://api.yourdomain.com`

### 2.3 发布流程
1. 使用微信开发者工具上传代码
2. 在微信公众平台提交审核
3. 审核通过后发布

## 3. 部署脚本

### 3.1 自动部署脚本
```bash
#!/bin/bash
# deploy.sh

echo "🚀 开始部署视频审核系统..."

# 拉取最新代码
git pull origin main

# 安装依赖
cd web-admin
npm install --production

# 构建前端
npm run build

# 重启服务
pm2 restart video-review-api

# 重载 Nginx
sudo nginx -t && sudo nginx -s reload

echo "✅ 部署完成！"
```

## 4. 监控和维护

### 4.1 日志监控
```bash
# 查看 PM2 日志
pm2 logs video-review-api

# 查看 Nginx 日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 4.2 性能监控
```bash
# PM2 监控
pm2 monit

# 系统资源监控
htop
df -h
```

### 4.3 备份策略
```bash
#!/bin/bash
# backup.sh - 数据库备份脚本

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/video-review"
DB_PATH="/var/www/video-review/data/db.sqlite3"

mkdir -p $BACKUP_DIR
cp $DB_PATH $BACKUP_DIR/db_backup_$DATE.sqlite3

# 保留最近30天的备份
find $BACKUP_DIR -name "db_backup_*.sqlite3" -mtime +30 -delete

echo "✅ 数据库备份完成: db_backup_$DATE.sqlite3"
```

## 5. 安全配置

### 5.1 防火墙设置
```bash
# 只开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw deny 3000   # 禁止直接访问 Node.js
```

### 5.2 定期更新
```bash
# 系统更新
sudo apt update && sudo apt upgrade -y

# Node.js 安全更新
npm audit fix
```

## 6. 故障排查

### 6.1 常见问题
- **端口占用**：`lsof -i :3000`
- **权限问题**：检查文件所有者和权限
- **内存不足**：增加服务器配置或优化代码
- **数据库锁定**：重启服务或检查并发访问

### 6.2 健康检查
```bash
# API 健康检查
curl -f http://localhost:3000/api/health || echo "API 服务异常"

# 数据库检查
sqlite3 /var/www/video-review/data/db.sqlite3 ".tables"
```
