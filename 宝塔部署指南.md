# 🚀 宝塔服务器部署指南

## 第一步：宝塔面板基础配置

### 1.1 安装必要软件
在宝塔面板 -> 软件商店中安装：
- **Nginx** (1.20+)
- **Node.js版本管理器** (安装 Node.js 18+)
- **PM2管理器** (进程管理)
- **SSL证书** (免费证书)

### 1.2 创建网站
1. 进入宝塔面板 -> 网站
2. 添加站点：
   - 域名：`admin.yourdomain.com` (管理后台)
   - 域名：`api.yourdomain.com` (API接口)
   - 根目录：`/www/wwwroot/video-review`

## 第二步：上传项目文件

### 2.1 方式一：通过宝塔文件管理器
1. 打开宝塔面板 -> 文件
2. 进入 `/www/wwwroot/video-review`
3. 上传项目压缩包并解压

### 2.2 方式二：通过Git (推荐)
```bash
# SSH连接服务器
cd /www/wwwroot/video-review
git clone https://github.com/your-username/your-repo.git .
```

## 第三步：后台API部署

### 3.1 安装依赖
```bash
cd /www/wwwroot/video-review/web-admin
npm install --production
```

### 3.2 修改生产环境配置
创建 `production-config.js`：
```javascript
module.exports = {
  port: 3000,
  cors: {
    origin: [
      'https://admin.yourdomain.com',
      'https://api.yourdomain.com'
    ]
  },
  database: {
    path: '/www/wwwroot/video-review/data/db.sqlite3'
  },
  uploads: {
    path: '/www/wwwroot/video-review/uploads'
  }
};
```

### 3.3 创建数据和上传目录
```bash
mkdir -p /www/wwwroot/video-review/data
mkdir -p /www/wwwroot/video-review/uploads
chmod 755 /www/wwwroot/video-review/data
chmod 755 /www/wwwroot/video-review/uploads
```

### 3.4 使用PM2启动服务
在宝塔面板 -> PM2管理器中：
1. 点击"添加项目"
2. 配置如下：
   - **项目名称**：video-review-api
   - **启动文件**：server.cjs
   - **项目目录**：/www/wwwroot/video-review/web-admin
   - **运行模式**：cluster
   - **实例数量**：2

或者通过命令行：
```bash
cd /www/wwwroot/video-review/web-admin
pm2 start server.cjs --name video-review-api -i 2
pm2 save
pm2 startup
```

## 第四步：前端管理后台部署

### 4.1 构建前端项目
```bash
cd /www/wwwroot/video-review/web-admin
npm run build
```

### 4.2 配置Nginx
在宝塔面板 -> 网站 -> admin.yourdomain.com -> 设置：

#### 网站目录设置：
- 运行目录：`/dist`
- 防跨站攻击：关闭

#### 伪静态规则：
```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

#### 反向代理设置：
添加反向代理：
- 代理名称：api
- 目标URL：http://127.0.0.1:3000
- 发送域名：$host
- 代理目录：/api

完整Nginx配置：
```nginx
server {
    listen 80;
    server_name admin.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name admin.yourdomain.com;
    root /www/wwwroot/video-review/web-admin/dist;
    index index.html;
    
    # SSL证书配置（宝塔自动生成）
    ssl_certificate /www/server/panel/vhost/cert/admin.yourdomain.com/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/admin.yourdomain.com/privkey.pem;
    
    # 前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 第五步：API域名配置

### 5.1 创建API站点
在宝塔面板中为 `api.yourdomain.com` 创建站点

### 5.2 配置API Nginx
```nginx
server {
    listen 80;
    server_name api.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;
    
    # SSL证书配置
    ssl_certificate /www/server/panel/vhost/cert/api.yourdomain.com/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/api.yourdomain.com/privkey.pem;
    
    # 客户端最大上传大小
    client_max_body_size 100M;
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 视频文件访问
    location /uploads/ {
        alias /www/wwwroot/video-review/uploads/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
    
    # 健康检查
    location /health {
        proxy_pass http://127.0.0.1:3000/api/health;
        access_log off;
    }
}
```

## 第六步：SSL证书配置

### 6.1 申请免费SSL证书
在宝塔面板 -> 网站 -> SSL中：
1. 选择"Let's Encrypt"
2. 输入域名：admin.yourdomain.com, api.yourdomain.com
3. 点击申请
4. 开启"强制HTTPS"

## 第七步：小程序配置修改

### 7.1 修改API地址
修改小程序中的API基础地址：

```javascript
// pages/upload/upload.js
const API_BASE = 'https://api.yourdomain.com';

// 上传视频
wx.uploadFile({
  url: `${API_BASE}/api/upload`,
  filePath: this.data.tempFilePath,
  name: 'videoFile',
  formData: {
    userId: wx.getStorageSync('userId'),
    title: this.data.title
  },
  success: (res) => {
    // 处理成功
  }
});

// 获取我的视频
wx.request({
  url: `${API_BASE}/api/my/videos`,
  data: {
    userId: wx.getStorageSync('userId')
  },
  success: (res) => {
    // 处理成功
  }
});
```

### 7.2 配置服务器域名
在微信公众平台 -> 开发 -> 开发设置中配置：
- **request合法域名**：`https://api.yourdomain.com`
- **uploadFile合法域名**：`https://api.yourdomain.com`
- **downloadFile合法域名**：`https://api.yourdomain.com`

## 第八步：测试和验证

### 8.1 后台API测试
```bash
# 健康检查
curl https://api.yourdomain.com/api/health

# 测试上传（需要实际文件）
curl -X POST https://api.yourdomain.com/api/upload \
  -F "videoFile=@test.mp4" \
  -F "userId=test123" \
  -F "title=测试视频"
```

### 8.2 管理后台测试
访问 `https://admin.yourdomain.com`：
1. 检查页面是否正常加载
2. 测试登录功能
3. 测试视频列表显示
4. 测试审核功能

### 8.3 小程序测试
1. 在微信开发者工具中测试
2. 上传视频功能
3. 查看我的视频列表

## 第九步：监控和维护

### 9.1 设置监控
在宝塔面板 -> 监控中：
1. 开启服务器监控
2. 设置告警通知
3. 监控PM2进程状态

### 9.2 日志管理
```bash
# 查看PM2日志
pm2 logs video-review-api

# 查看Nginx日志
tail -f /www/wwwroot/video-review/logs/access.log
tail -f /www/wwwroot/video-review/logs/error.log
```

### 9.3 备份策略
在宝塔面板 -> 计划任务中设置：
1. 数据库备份（每天）
2. 网站备份（每周）
3. 日志清理（每月）

## 第十步：安全配置

### 10.1 防火墙设置
在宝塔面板 -> 安全中：
1. 开启防火墙
2. 只开放必要端口：22, 80, 443, 8888
3. 禁用不必要的端口

### 10.2 安全加固
1. 修改SSH端口
2. 禁用root登录
3. 设置复杂密码
4. 定期更新系统

## 常见问题解决

### Q1: PM2进程启动失败
```bash
# 检查Node.js版本
node -v

# 检查端口占用
netstat -tlnp | grep :3000

# 重启PM2
pm2 restart all
```

### Q2: 文件上传失败
```bash
# 检查目录权限
ls -la /www/wwwroot/video-review/uploads

# 修改权限
chmod 755 /www/wwwroot/video-review/uploads
```

### Q3: SSL证书问题
1. 检查域名解析是否正确
2. 确保80端口可访问
3. 重新申请证书

### Q4: 跨域问题
检查Nginx配置中的CORS设置，确保包含正确的域名。
