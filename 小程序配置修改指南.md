# 📱 小程序线上配置修改指南

## 第一步：修改API基础地址

### 1.1 创建配置文件
在小程序根目录创建 `config.js`：

```javascript
// config.js
const config = {
  // 开发环境
  development: {
    apiBase: 'http://localhost:3000'
  },
  // 生产环境
  production: {
    apiBase: 'https://api.yourdomain.com'  // 替换为你的域名
  }
};

// 获取当前环境配置
function getConfig() {
  const accountInfo = wx.getAccountInfoSync();
  const envVersion = accountInfo.miniProgram.envVersion;
  
  // develop: 开发版, trial: 体验版, release: 正式版
  if (envVersion === 'release') {
    return config.production;
  } else {
    return config.development;
  }
}

module.exports = {
  getConfig
};
```

### 1.2 修改上传页面
修改 `pages/upload/upload.js`：

```javascript
// pages/upload/upload.js
const { getConfig } = require('../../config.js');
const config = getConfig();

Page({
  data: {
    tempFilePath: '',
    poster: '',
    title: '',
    isUploading: false,
    uploadProgress: 0,
  },

  // ... 其他方法保持不变

  /**
   * 上传视频
   */
  uploadVideo() {
    if (!this.data.tempFilePath) {
      wx.showToast({ title: '请先选择视频', icon: 'none' });
      return;
    }

    if (!this.data.title.trim()) {
      wx.showToast({ title: '请输入视频标题', icon: 'none' });
      return;
    }

    this.setData({ isUploading: true, uploadProgress: 0 });

    // 获取用户ID
    let userId = wx.getStorageSync('userId');
    if (!userId) {
      userId = 'user_' + Date.now();
      wx.setStorageSync('userId', userId);
    }

    const uploadTask = wx.uploadFile({
      url: `${config.apiBase}/api/upload`,  // 使用配置中的API地址
      filePath: this.data.tempFilePath,
      name: 'videoFile',
      formData: {
        userId: userId,
        title: this.data.title
      },
      success: (res) => {
        console.log('上传成功', res);
        if (res.statusCode === 200) {
          const data = JSON.parse(res.data);
          wx.showToast({ title: '上传成功', icon: 'success' });
          
          // 清空表单
          this.setData({
            tempFilePath: '',
            poster: '',
            title: ''
          });
          
          // 跳转到我的视频页面
          wx.switchTab({
            url: '/pages/my-uploads/my-uploads'
          });
        } else {
          wx.showToast({ title: '上传失败', icon: 'none' });
        }
      },
      fail: (err) => {
        console.error('上传失败', err);
        wx.showToast({ title: '上传失败', icon: 'none' });
      },
      complete: () => {
        this.setData({ isUploading: false, uploadProgress: 0 });
      }
    });

    // 监听上传进度
    uploadTask.onProgressUpdate((res) => {
      this.setData({
        uploadProgress: res.progress
      });
    });
  }
});
```

### 1.3 修改我的视频页面
修改 `pages/my-uploads/my-uploads.js`：

```javascript
// pages/my-uploads/my-uploads.js
const { getConfig } = require('../../config.js');
const config = getConfig();

Page({
  data: {
    videos: [],
    loading: true
  },

  onLoad() {
    this.loadMyVideos();
  },

  onShow() {
    this.loadMyVideos();
  },

  /**
   * 加载我的视频
   */
  loadMyVideos() {
    const userId = wx.getStorageSync('userId');
    if (!userId) {
      this.setData({ loading: false });
      return;
    }

    wx.request({
      url: `${config.apiBase}/api/my/videos`,  // 使用配置中的API地址
      data: { userId },
      method: 'GET',
      success: (res) => {
        console.log('获取视频列表成功', res);
        if (res.statusCode === 200) {
          this.setData({
            videos: res.data || []
          });
        } else {
          wx.showToast({ title: '获取视频列表失败', icon: 'none' });
        }
      },
      fail: (err) => {
        console.error('获取视频列表失败', err);
        wx.showToast({ title: '网络错误', icon: 'none' });
      },
      complete: () => {
        this.setData({ loading: false });
      }
    });
  },

  /**
   * 刷新列表
   */
  onRefresh() {
    this.setData({ loading: true });
    this.loadMyVideos();
  }
});
```

## 第二步：配置服务器域名

### 2.1 登录微信公众平台
1. 访问 https://mp.weixin.qq.com/
2. 使用小程序账号登录

### 2.2 配置服务器域名
1. 进入 **开发** -> **开发管理** -> **开发设置**
2. 找到 **服务器域名** 配置
3. 配置以下域名：

```
request合法域名：
https://api.yourdomain.com

uploadFile合法域名：
https://api.yourdomain.com

downloadFile合法域名：
https://api.yourdomain.com
```

### 2.3 注意事项
- 域名必须是HTTPS
- 域名必须已备案
- 每月只能修改5次
- 配置后需要重新发布小程序

## 第三步：测试配置

### 3.1 本地测试
在微信开发者工具中：
1. 打开项目设置
2. 勾选 **不校验合法域名**
3. 测试上传和获取功能

### 3.2 真机测试
1. 上传体验版
2. 使用手机微信扫码
3. 测试完整功能流程

## 第四步：发布小程序

### 4.1 上传代码
在微信开发者工具中：
1. 点击 **上传**
2. 填写版本号和项目备注
3. 上传成功后在公众平台查看

### 4.2 提交审核
在微信公众平台：
1. 进入 **版本管理**
2. 选择开发版本
3. 点击 **提交审核**
4. 填写审核信息

### 4.3 发布上线
审核通过后：
1. 在版本管理中找到审核通过的版本
2. 点击 **发布**
3. 确认发布

## 第五步：环境变量配置（可选）

### 5.1 使用云开发环境变量
如果使用微信云开发，可以配置环境变量：

```javascript
// 在云函数中使用
const cloud = require('wx-server-sdk');
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

// 获取环境变量
const apiBase = process.env.API_BASE || 'https://api.yourdomain.com';
```

### 5.2 动态配置
也可以通过接口动态获取配置：

```javascript
// utils/config.js
let cachedConfig = null;

async function getRemoteConfig() {
  if (cachedConfig) {
    return cachedConfig;
  }
  
  try {
    const res = await wx.request({
      url: 'https://api.yourdomain.com/api/config',
      method: 'GET'
    });
    
    if (res.statusCode === 200) {
      cachedConfig = res.data;
      return cachedConfig;
    }
  } catch (error) {
    console.error('获取远程配置失败', error);
  }
  
  // 返回默认配置
  return {
    apiBase: 'https://api.yourdomain.com'
  };
}

module.exports = {
  getRemoteConfig
};
```

## 第六步：错误处理和日志

### 6.1 统一错误处理
创建 `utils/request.js`：

```javascript
// utils/request.js
const { getConfig } = require('../config.js');

function request(options) {
  const config = getConfig();
  
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${config.apiBase}${options.url}`,
      method: options.method || 'GET',
      data: options.data || {},
      header: options.header || {},
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          console.error('请求失败', res);
          reject(new Error(`请求失败: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        console.error('网络错误', err);
        reject(new Error('网络错误'));
      }
    });
  });
}

function uploadFile(options) {
  const config = getConfig();
  
  return new Promise((resolve, reject) => {
    const uploadTask = wx.uploadFile({
      url: `${config.apiBase}${options.url}`,
      filePath: options.filePath,
      name: options.name,
      formData: options.formData || {},
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            const data = JSON.parse(res.data);
            resolve(data);
          } catch (error) {
            resolve(res.data);
          }
        } else {
          reject(new Error(`上传失败: ${res.statusCode}`));
        }
      },
      fail: reject
    });
    
    if (options.onProgress) {
      uploadTask.onProgressUpdate(options.onProgress);
    }
  });
}

module.exports = {
  request,
  uploadFile
};
```

### 6.2 使用统一请求方法
修改页面代码使用统一的请求方法：

```javascript
// pages/upload/upload.js
const { uploadFile } = require('../../utils/request.js');

Page({
  // ... 其他代码

  async uploadVideo() {
    // ... 验证代码

    try {
      this.setData({ isUploading: true });
      
      const result = await uploadFile({
        url: '/api/upload',
        filePath: this.data.tempFilePath,
        name: 'videoFile',
        formData: {
          userId: userId,
          title: this.data.title
        },
        onProgress: (res) => {
          this.setData({ uploadProgress: res.progress });
        }
      });
      
      wx.showToast({ title: '上传成功', icon: 'success' });
      // 处理成功逻辑...
      
    } catch (error) {
      console.error('上传失败', error);
      wx.showToast({ title: error.message || '上传失败', icon: 'none' });
    } finally {
      this.setData({ isUploading: false, uploadProgress: 0 });
    }
  }
});
```

## 常见问题解决

### Q1: 域名配置后仍然无法访问
- 检查域名是否正确配置HTTPS
- 确认SSL证书是否有效
- 检查服务器防火墙设置

### Q2: 上传文件失败
- 检查文件大小限制
- 确认服务器上传目录权限
- 查看服务器日志

### Q3: 小程序审核被拒
- 确保功能完整可用
- 检查是否有违规内容
- 完善隐私政策和用户协议

### Q4: 真机测试正常但发布后异常
- 检查生产环境配置
- 确认域名白名单设置
- 查看小程序后台错误日志
